﻿unit TableDataManager;

interface

uses
  System.SysUtils, System.Classes, System.Generics.Collections,
  Data.DB, FireDAC.Comp.Client, FireDAC.Comp.DataSet, DBConnection,
  Vcl.DBGrids, Vcl.ComCtrls, CustomDBGrid, ExceptionLogger;

type
  // 表数据管理器类
  TTableDataManager = class
  private
    FConnection: TFDConnection;
    FCurrentQuery: TFDQuery;
    FDataSource: TDataSource;
    FFormulaDBGrid: TFormulaDBGrid;
    FCurrentTable: string;
    FPrimaryKeyFields: TStringList;
    FIsEditing: Boolean;
    FCurrentMemTable: TFDMemTable; // 当前内存表，用于Excel MemTable
    FCurrentExcelFile: string; // 当前Excel文件路径

    // 私有方法
    function GetPrimaryKeyFields(const ATableName: string): TStringList;
    procedure SetupFieldEvents;

  public
    constructor Create(AConnection: TFDConnection; ADataSource: TDataSource; AFormulaDBGrid: TFormulaDBGrid);
    destructor Destroy; override;

    // 数据操作方法
    procedure LoadTableData(const ATableName: string);
    procedure RefreshTableData;
    procedure SaveCurrentRecord;

    // 数据导入导出
    procedure ExportTableData(const ATableName: string);
    function GetTableNames: TStringList;

    // Excel数据处理方法 - 与数据库表格操作保持一致
    procedure LoadExcelData(MemTable: TFDMemTable; const ExcelFilePath: string);
    procedure SaveExcelData(const ExcelFilePath: string);
    procedure RefreshExcelData;
    function IsExcelDataMode: Boolean;

    // 数据状态管理
    procedure CleanupCurrentData;

    // 属性
    property CurrentTable: string read FCurrentTable;
    property CurrentQuery: TFDQuery read FCurrentQuery;
    property IsEditing: Boolean read FIsEditing write FIsEditing;
    property PrimaryKeyFields: TStringList read FPrimaryKeyFields;
    property CurrentMemTable: TFDMemTable read FCurrentMemTable;
  end;

implementation

uses
  System.Math, System.StrUtils, Vcl.Dialogs, UnifiedExcelProcessor_POI;

constructor TTableDataManager.Create(AConnection: TFDConnection; ADataSource: TDataSource; AFormulaDBGrid: TFormulaDBGrid);
begin
  inherited Create;
  FConnection := AConnection;
  FDataSource := ADataSource;
  FFormulaDBGrid := AFormulaDBGrid;
  FCurrentQuery := TFDQuery.Create(nil);
  FPrimaryKeyFields := TStringList.Create;
  FIsEditing := False;
  FCurrentTable := '';
  FCurrentMemTable := nil;
  FCurrentExcelFile := '';
end;

destructor TTableDataManager.Destroy;
begin
  // 清理数据源连接，避免悬空指针
  if Assigned(FDataSource) then
    FDataSource.DataSet := nil;

  if Assigned(FCurrentMemTable) then
    FCurrentMemTable.Free;
  if Assigned(FCurrentQuery) then
    FCurrentQuery.Free;
  if Assigned(FPrimaryKeyFields) then
    FPrimaryKeyFields.Free;
  inherited;
end;

procedure TTableDataManager.LoadTableData(const ATableName: string);
var
  IsSQLite, IsExcel: Boolean;
  i: Integer;
begin
  if not Assigned(FConnection) then
    Exit;

  try
    FCurrentTable := ATableName;

    // 完全清理之前的数据状态
    CleanupCurrentData;

    // 直接从连接参数判断数据库类型，避免创建额外的DBManager实例
    IsExcel := (FConnection.Params.Values['ExcelMode'] = '1');
    IsSQLite := (FConnection.Params.DriverID = 'SQLite') and not IsExcel;

    // Handle Excel files - 使用统一的Excel处理
    if IsExcel then
    begin
      try
        // 获取Excel文件路径
        var ExcelFilePath: string := FConnection.Params.Values['ExcelPath'];

        // 使用统一的Excel API加载数据
        FCurrentMemTable := TExcelAPI.ReadSheetToDataSet(ExcelFilePath, ATableName);

        if Assigned(FCurrentMemTable) then
        begin
          // Connect to data source
          if Assigned(FDataSource) then
          begin
            FDataSource.DataSet := FCurrentMemTable;
            FDataSource.AutoEdit := False;
          end;

          // Update DBGrid display
          if Assigned(FFormulaDBGrid) and Assigned(FDataSource) then
          begin
            FFormulaDBGrid.DataSource := FDataSource;
            FFormulaDBGrid.Refresh;

            // Set column widths
            for i := 0 to FFormulaDBGrid.Columns.Count - 1 do
            begin
              FFormulaDBGrid.Columns[i].Width := 120; // Default column width
            end;
          end;
        end;
      except
        on E: Exception do
        begin
          TExceptionLogger.Instance.LogError('TableDataManager', string('Excel数据加载失败: ') + E.Message, E, string('LoadTableData'));
        end;
      end;
      Exit; // Excel处理完成，退出
    end;

    // For regular databases, check connection
    if not FConnection.Connected then
      Exit;

    // Clear primary key fields
    FPrimaryKeyFields.Clear;
    FPrimaryKeyFields.AddStrings(GetPrimaryKeyFields(ATableName));

    // Setup query
    FCurrentQuery.Close;
    FCurrentQuery.Connection := FConnection;

    // Build SQL based on database type
    if IsSQLite then
    begin
      // SQLite query
      FCurrentQuery.SQL.Text := Format('SELECT * FROM "%s"', [ATableName]);
    end
    else
    begin
      // Access and other databases
      FCurrentQuery.SQL.Text := Format('SELECT * FROM [%s]', [ATableName]);
    end;

    // Execute query
    FCurrentQuery.Open;

    // Connect to data source
    if Assigned(FDataSource) then
    begin
      FDataSource.DataSet := FCurrentQuery;
      FDataSource.AutoEdit := False;
    end;

    // Update DBGrid display
    if Assigned(FFormulaDBGrid) and Assigned(FDataSource) then
    begin
      FFormulaDBGrid.DataSource := FDataSource;
      FFormulaDBGrid.Refresh;

      // Set column widths
      for i := 0 to FFormulaDBGrid.Columns.Count - 1 do
      begin
        FFormulaDBGrid.Columns[i].Width := 120; // Default column width
      end;
    end;

    // Setup field events
    SetupFieldEvents;

  except
    on E: Exception do
    begin
      TExceptionLogger.Instance.LogError('TableDataManager', string('数据库表加载失败: ') + E.Message, E, string('LoadTableData'));
    end;
  end;
end;

procedure TTableDataManager.RefreshTableData;
begin
  if FCurrentTable <> '' then
    LoadTableData(FCurrentTable);
end;

procedure TTableDataManager.SaveCurrentRecord;
var
  DataSet: TDataSet;
  IsMemTable: Boolean;
begin
  try
    // 确定当前活动的数据集
    DataSet := nil;
    IsMemTable := False;

    // 优先检查Excel数据（MemTable）
    if Assigned(FCurrentMemTable) and FCurrentMemTable.Active then
    begin
      DataSet := FCurrentMemTable;
      IsMemTable := True;
    end
    else if Assigned(FCurrentQuery) and FCurrentQuery.Active then
    begin
      DataSet := FCurrentQuery;
      IsMemTable := False;
    end;

    if not Assigned(DataSet) then
    begin
      LogAndShowWarning('TableDataManager', '无有效的数据源可保存', '尝试保存记录但无有效数据源');
      Exit;
    end;

    // 统一的数据保存处理
    if DataSet.State in [dsEdit, dsInsert] then
    begin
      DataSet.Post;

      // 对于内存表，应用缓存更新
      if IsMemTable and (DataSet is TFDMemTable) then
      begin
        with TFDMemTable(DataSet) do
        begin
          if CachedUpdates then
            ApplyUpdates;
        end;
      end;
    end;

  except
    on E: Exception do
    begin
      // 如果保存失败，尝试取消编辑状态
      if Assigned(DataSet) and (DataSet.State in [dsEdit, dsInsert]) then
      begin
        try
          DataSet.Cancel;
        except
          // 忽略取消操作的异常
        end;
      end;
      LogAndShowError('TableDataManager', '保存记录失败', E, '保存当前记录', True);
    end;
  end;
end;

function TTableDataManager.GetPrimaryKeyFields(const ATableName: string): TStringList;
var
  Query: TFDQuery;
  DBManager: DBConnection.TDBConnectionManager;
begin
  Result := TStringList.Create;

  if not Assigned(FConnection) or not FConnection.Connected then
    Exit;

  DBManager := DBConnection.TDBConnectionManager.Create(FConnection, nil);
  try
    Query := DBManager.GetTableStructure(ATableName);
    if Assigned(Query) then
    begin
      try
        Query.First;
        while not Query.Eof do
        begin
          // 检查是否为主键字段
          if Query.FieldByName('pk').AsInteger = 1 then
            Result.Add(Query.FieldByName('name').AsString);
          Query.Next;
        end;
      finally
        Query.Free;
      end;
    end;
  finally
    DBManager.Free;
  end;
end;

procedure TTableDataManager.SetupFieldEvents;
var
  i: Integer;
  DataSet: TDataSet;
begin
  // 确定当前活动的数据集
  DataSet := nil;
  if IsExcelDataMode then
  begin
    // Excel模式：使用MemTable
    if Assigned(FCurrentMemTable) and FCurrentMemTable.Active then
      DataSet := FCurrentMemTable;
  end
  else
  begin
    // 数据库模式：使用Query
    if Assigned(FCurrentQuery) and FCurrentQuery.Active then
      DataSet := FCurrentQuery;
  end;

  if not Assigned(DataSet) then
    Exit;

  // 设置字段事件处理器
  for i := 0 to DataSet.FieldCount - 1 do
  begin
    // 字段事件处理已移动到FormulaEngine
    // 这里可以为Excel和数据库数据设置统一的字段事件
  end;
end;

// 导出表数据到Excel文件
procedure TTableDataManager.ExportTableData(const ATableName: string);
var
  SaveDialog: TSaveDialog;
  DataSet: TDataSet;
  FileStream: TFileStream;
  Writer: TStreamWriter;
  Line: string;
  I: Integer;
begin
  // 检查是否有活动数据集
  DataSet := nil;
  if Assigned(FCurrentMemTable) and FCurrentMemTable.Active then
    DataSet := FCurrentMemTable
  else if Assigned(FCurrentQuery) and FCurrentQuery.Active then
    DataSet := FCurrentQuery;

  if not Assigned(DataSet) then
  begin
    LogAndShowWarning('TableDataManager', string('没有可导出的数据'), string('尝试导出数据但无可用数据集'));
    Exit;
  end;

  // 显示保存对话框
  SaveDialog := TSaveDialog.Create(nil);
  try
    SaveDialog.Title := string('导出表数据到Excel');
    SaveDialog.DefaultExt := 'xls';
    SaveDialog.Filter := string('Excel文件 (*.xls)|*.xls|CSV文件 (*.csv)|*.csv|所有文件 (*.*)|*.*');
    SaveDialog.FileName := ATableName + '.xls';

    if SaveDialog.Execute then
    begin
      try
        // 检查文件扩展名决定导出格式
        if LowerCase(ExtractFileExt(SaveDialog.FileName)) = '.xls' then
        begin
          // 导出为XLS格式
          if TExcelAPI.WriteDataSetToExcel(DataSet, SaveDialog.FileName, ATableName) then
            LogInfo('TableDataManager', string('数据已成功导出到Excel文件: ') + SaveDialog.FileName, string('导出数据到Excel'))
          else
            LogAndShowError('TableDataManager', string('导出Excel文件失败'), nil, string('导出数据到Excel'), True);
        end
        else
        begin
          // 导出为CSV格式
          FileStream := TFileStream.Create(SaveDialog.FileName, fmCreate);
          try
            Writer := TStreamWriter.Create(FileStream, TEncoding.UTF8);
            try

              // 写入表头
              Line := '';
              for I := 0 to DataSet.FieldCount - 1 do
              begin
                if I > 0 then
                  Line := Line + ',';
                Line := Line + '"' + DataSet.Fields[I].FieldName + '"';
              end;
              Writer.WriteLine(Line);

              // 写入数据行
              DataSet.First;
              while not DataSet.Eof do
              begin
                Line := '';
                for I := 0 to DataSet.FieldCount - 1 do
                begin
                  if I > 0 then
                    Line := Line + ',';
                  Line := Line + '"' + StringReplace(DataSet.Fields[I].AsString, '"', '""', [rfReplaceAll]) + '"';
                end;
                Writer.WriteLine(Line);
                DataSet.Next;
              end;

              LogInfo('TableDataManager', string('数据已成功导出到CSV文件: ') + SaveDialog.FileName, string('导出数据到CSV'));
            finally
              Writer.Free;
            end;
          finally
            FileStream.Free;
          end;
        end;
      except
        on E: Exception do
          LogAndShowError('TableDataManager', string('导出失败'), E, string('导出数据'), True);
      end;
    end;
  finally
    SaveDialog.Free;
  end;
end;



function TTableDataManager.GetTableNames: TStringList;
var
  DBManager: DBConnection.TDBConnectionManager;
  Tables: TStringList;
begin
  Result := TStringList.Create;

  if not Assigned(FConnection) or not FConnection.Connected then
    Exit;

  DBManager := DBConnection.TDBConnectionManager.Create(FConnection, nil);
  try
    Tables := DBManager.GetTableNames;
    if Assigned(Tables) then
    begin
      Result.Assign(Tables);
      Tables.Free;
    end;
  finally
    DBManager.Free;
  end;
end;

// ============================================================================
// Excel数据处理方法 - 与数据库表格操作保持一致
// ============================================================================

// 加载Excel数据到管理器中
procedure TTableDataManager.LoadExcelData(MemTable: TFDMemTable; const ExcelFilePath: string);
begin
  try
    // 完全清理之前的数据状态
    CleanupCurrentData;

    // 检查传入的MemTable是否有效
    if not Assigned(MemTable) then
    begin
      TExceptionLogger.Instance.LogError('TableDataManager', string('LoadExcelData: MemTable参数为空'), nil, string('Excel数据加载'));
      Exit;
    end;

    // 设置当前Excel数据
    FCurrentMemTable := MemTable;
    FCurrentExcelFile := ExcelFilePath;
    FCurrentTable := 'Excel:' + ExtractFileName(ExcelFilePath);

    // 确保MemTable处于活动状态
    if not FCurrentMemTable.Active then
      FCurrentMemTable.Active := True;

    // 设置MemTable编辑属性
    FCurrentMemTable.CachedUpdates := True; // 启用缓存更新
    FCurrentMemTable.IndexFieldNames := ''; // 清除索引，允许编辑

    // 连接到数据源
    if Assigned(FDataSource) then
    begin
      FDataSource.DataSet := FCurrentMemTable;
      FDataSource.AutoEdit := True; // 启用自动编辑
    end;

    // 设置DBGrid为Excel编辑模式
    if Assigned(FFormulaDBGrid) then
    begin
      FFormulaDBGrid.DataSource := FDataSource;

      // 重置编辑状态，这会根据数据类型自动设置编辑模式
      FFormulaDBGrid.ResetEditingState;
      FFormulaDBGrid.AllowFormulaInput := True;
      FFormulaDBGrid.Refresh;
    end;

    // 设置编辑状态
    FIsEditing := True;

    // 设置字段事件（现在会正确处理MemTable）
    SetupFieldEvents;

  except
    on E: Exception do
    begin
      LogAndShowError('TableDataManager', string('加载Excel数据失败'), E, string('加载Excel数据'), True);
    end;
  end;
end;

// 保存Excel数据
procedure TTableDataManager.SaveExcelData(const ExcelFilePath: string);
var
  ActualFilePath: string;
begin
  LogInfo('TableDataManager', 'SaveExcelData: 开始保存Excel数据到: ' + ExcelFilePath, 'Excel保存调试');

  if not Assigned(FCurrentMemTable) then
  begin
    LogInfo('TableDataManager', 'SaveExcelData: FCurrentMemTable未分配', 'Excel保存调试');
    LogAndShowWarning('TableDataManager', string('当前没有活动的Excel数据'), string('尝试保存Excel数据但没有数据源'));
    Exit;
  end;

  if not FCurrentMemTable.Active then
  begin
    LogInfo('TableDataManager', 'SaveExcelData: FCurrentMemTable不活动', 'Excel保存调试');
    LogAndShowWarning('TableDataManager', string('当前没有活动的Excel数据'), string('尝试保存Excel数据但没有数据源'));
    Exit;
  end;

  LogInfo('TableDataManager', 'SaveExcelData: MemTable检查通过，记录数: ' + IntToStr(FCurrentMemTable.RecordCount), 'Excel保存调试');

  try
    // 确定实际的文件路径
    if ExcelFilePath <> '' then
      ActualFilePath := ExcelFilePath
    else
      ActualFilePath := FCurrentExcelFile;

    LogInfo('TableDataManager', 'SaveExcelData: 实际文件路径: ' + ActualFilePath, 'Excel保存调试');

    if ActualFilePath = '' then
    begin
      LogAndShowError('TableDataManager', string('无法确定Excel文件路径'), nil, string('保存Excel数据'), True);
      Exit;
    end;

    // 确保数据已提交
    LogInfo('TableDataManager', 'SaveExcelData: 开始提交数据', 'Excel保存调试');
    if Assigned(FCurrentMemTable) and FCurrentMemTable.Active then
    begin
      if FCurrentMemTable.State in [dsEdit, dsInsert] then
      begin
        LogInfo('TableDataManager', 'SaveExcelData: 执行Post操作', 'Excel保存调试');
        FCurrentMemTable.Post;
      end;

      // 应用缓存的更新
      if FCurrentMemTable.CachedUpdates then
      begin
        LogInfo('TableDataManager', 'SaveExcelData: 执行ApplyUpdates', 'Excel保存调试');
        FCurrentMemTable.ApplyUpdates;
      end;
    end;

    // 使用Excel写入功能
    LogInfo('TableDataManager', 'SaveExcelData: 调用TExcelAPI.WriteDataSetToExcel', 'Excel保存调试');
    try
      if TExcelAPI.WriteDataSetToExcel(FCurrentMemTable, ActualFilePath, 'Sheet1') then
      begin
        LogInfo('TableDataManager', string('Excel数据已成功保存到: ') + ActualFilePath, string('保存Excel数据'));
        LogInfo('TableDataManager', 'SaveExcelData: WriteDataSetToExcel返回True', 'Excel保存调试');
      end
      else
      begin
        LogInfo('TableDataManager', 'SaveExcelData: WriteDataSetToExcel返回False', 'Excel保存调试');
        LogAndShowError('TableDataManager', string('Excel数据保存失败'), nil, string('保存Excel数据'), True);
      end;
    except
      on E: Exception do
      begin
        LogInfo('TableDataManager', 'SaveExcelData: WriteDataSetToExcel抛出异常: ' + E.Message, 'Excel保存调试');
        LogAndShowError('TableDataManager', string('Excel数据保存异常: ') + E.Message, E, string('保存Excel数据'), True);
      end;
    end;

  except
    on E: Exception do
    begin
      LogAndShowError('TableDataManager', string('保存Excel数据失败'), E, string('保存Excel数据'), True);
    end;
  end;
end;

// 刷新Excel数据
procedure TTableDataManager.RefreshExcelData;
begin
  if not IsExcelDataMode then
  begin
    LogAndShowWarning('TableDataManager', string('当前不是Excel数据模式'), string('尝试刷新Excel数据但不在Excel模式'));
    Exit;
  end;

  // 重新加载Excel文件
  if FCurrentExcelFile <> '' then
  begin
    LogInfo('TableDataManager', string('Excel数据刷新功能需要重新加载文件: ') + FCurrentExcelFile, string('刷新Excel数据'));
    // 这里可以触发重新加载Excel文件的事件
  end;
end;

// 检查是否为Excel数据模式
function TTableDataManager.IsExcelDataMode: Boolean;
begin
  // 简化Excel数据模式检测 - 只要有活动的MemTable就认为是Excel模式
  Result := Assigned(FCurrentMemTable) and FCurrentMemTable.Active;

  // 添加调试日志
  LogInfo('TableDataManager', 'IsExcelDataMode: FCurrentMemTable分配状态: ' + BoolToStr(Assigned(FCurrentMemTable), True), 'Excel模式调试');
  if Assigned(FCurrentMemTable) then
    LogInfo('TableDataManager', 'IsExcelDataMode: FCurrentMemTable活动状态: ' + BoolToStr(FCurrentMemTable.Active, True), 'Excel模式调试');
  LogInfo('TableDataManager', 'IsExcelDataMode: 返回结果: ' + BoolToStr(Result, True), 'Excel模式调试');
end;

// 清理当前数据状态
procedure TTableDataManager.CleanupCurrentData;
begin
  try
    // 清理数据源连接
    if Assigned(FDataSource) then
      FDataSource.DataSet := nil;

    // 清理数据库查询（但不释放对象，只关闭）
    if Assigned(FCurrentQuery) then
    begin
      try
        if FCurrentQuery.Active then
          FCurrentQuery.Close;
        FCurrentQuery.SQL.Clear;
      except
        // 忽略关闭查询时的错误
      end;
    end;

    // 清理内存表
    if Assigned(FCurrentMemTable) then
    begin
      try
        if FCurrentMemTable.Active then
          FCurrentMemTable.Close;
      except
        // 忽略关闭内存表时的错误
      end;
      FreeAndNil(FCurrentMemTable);
    end;

    // 重置状态变量
    FCurrentTable := '';
    FCurrentExcelFile := '';
    FIsEditing := False;

    // 清理主键字段列表
    if Assigned(FPrimaryKeyFields) then
      FPrimaryKeyFields.Clear;

    // 重置DBGrid的编辑状态
    if Assigned(FFormulaDBGrid) then
    begin
      try
        FFormulaDBGrid.ResetEditingState;
      except
        // 忽略重置编辑状态时的错误
      end;
    end;

  except
    on E: Exception do
    begin
      // 记录清理过程中的错误，但不抛出异常
      TExceptionLogger.Instance.LogError('TableDataManager', string('清理数据状态时发生错误'), E, string('数据清理'));
    end;
  end;
end;

end.
