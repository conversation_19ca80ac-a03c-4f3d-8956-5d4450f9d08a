# Excel数据库读取功能测试用例

## 测试环境准备

### 1. 创建测试Excel文件

创建以下测试文件在`测试目录\Mud2\DB\`中：

#### vv_Magic.xls
```
名称        | 类型    | 威力  | 消耗MP | 描述
火球术      | 攻击    | 100   | 20     | 基础火系攻击魔法
治疗术      |恢复    | 50    | 15     | 恢复生命值
闪电术      | 攻击    | 120   | 25     | 雷系攻击魔法
```

#### vv_Monster.xls
```
名称        | 等级  | 生命值 | 攻击力 | 防御力 | 经验值
小怪物      | 1     | 100    | 10     | 5      | 10
中等怪物    | 5     | 500    | 50     | 25     | 100
大怪物      | 10    | 1000   | 100    | 50     | 500
```

#### vv_StdItem.xls
```
名称        | 类型    | 价格  | 重量  | 描述
木剑        | 武器    | 100   | 2     | 基础武器
皮甲        | 防具    | 200   | 5     | 基础防具
生命药水    | 消耗品  | 50    | 1     | 恢复生命值
```

### 2. 配置文件设置

创建`Config.ini`文件：
```ini
[GameConf]
ServerDBType=2

[DB]
DBType=Excel
ExcelDir=Mud2\DB
```

## 测试用例

### 测试用例1：自动检测Excel数据库

**目标**：验证程序能够自动检测并连接Excel数据库

**步骤**：
1. 将测试Excel文件放置在`Mud2\DB\`目录
2. 设置`Config.ini`中`ServerDBType=2`
3. 启动程序
4. 观察是否自动连接到Excel数据库

**期望结果**：
- 程序自动检测到Excel文件
- 状态栏显示"已连接到数据库"
- 数据库列表显示Excel文件作为表

### 测试用例2：手动连接Excel数据库

**目标**：验证手动连接Excel数据库功能

**步骤**：
1. 点击"连接"按钮
2. 在连接对话框中选择"Excel数据库"
3. 浏览并选择包含Excel文件的目录
4. 点击"连接"

**期望结果**：
- 连接对话框正确显示Excel数据库选项
- 能够成功连接到Excel数据库
- 表列表显示Excel文件

### 测试用例3：Excel浏览器功能

**目标**：验证Excel浏览器的各项功能

**步骤**：
1. 从菜单选择"文件" → "Excel数据库浏览器"
2. 浏览并选择Excel文件目录
3. 查看文件列表
4. 选择文件查看信息
5. 双击文件加载数据

**期望结果**：
- Excel浏览器窗口正确打开
- 文件列表显示所有Excel文件
- 文件信息正确显示
- 数据正确加载到网格中

### 测试用例4：数据读取验证

**目标**：验证Excel数据读取的准确性

**步骤**：
1. 连接到Excel数据库
2. 选择`vv_Magic`表
3. 查看数据内容
4. 验证字段名和数据类型
5. 检查数据完整性

**期望结果**：
- 表结构正确识别
- 字段名与Excel标题行一致
- 数据类型自动识别正确
- 所有数据行正确读取

### 测试用例5：多文件处理

**目标**：验证处理多个Excel文件的能力

**步骤**：
1. 在目录中放置多个Excel文件
2. 连接到Excel数据库
3. 查看表列表
4. 分别访问不同的表
5. 验证数据隔离性

**期望结果**：
- 所有Excel文件都被识别为表
- 表名正确显示（去除文件扩展名）
- 不同表的数据相互独立
- 切换表时数据正确更新

### 测试用例6：错误处理

**目标**：验证各种错误情况的处理

**步骤**：
1. 测试Excel未安装的情况
2. 测试文件权限不足的情况
3. 测试损坏的Excel文件
4. 测试空目录的情况
5. 测试网络路径的Excel文件

**期望结果**：
- 错误信息清晰明确
- 程序不会崩溃
- 提供合理的解决建议
- 能够优雅地处理异常

### 测试用例7：性能测试

**目标**：验证大文件和多文件的处理性能

**步骤**：
1. 创建包含1000行数据的Excel文件
2. 创建包含10个Excel文件的目录
3. 测试连接时间
4. 测试数据加载时间
5. 测试内存使用情况

**期望结果**：
- 连接时间在可接受范围内（<10秒）
- 数据加载流畅
- 内存使用合理
- 无内存泄漏

## 测试检查清单

### 功能测试
- [ ] 自动检测Excel数据库
- [ ] 手动连接Excel数据库
- [ ] Excel浏览器界面
- [ ] 文件信息显示
- [ ] 数据读取和显示
- [ ] 多文件支持
- [ ] 表切换功能

### 兼容性测试
- [ ] Excel 2003 (.xls)
- [ ] Excel 2007+ (.xlsx)
- [ ] LibreOffice Calc
- [ ] WPS表格
- [ ] 不同编码的Excel文件

### 错误处理测试
- [ ] Excel未安装
- [ ] 文件不存在
- [ ] 文件权限不足
- [ ] 文件格式错误
- [ ] 空文件处理
- [ ] 网络连接问题

### 性能测试
- [ ] 大文件处理（>1MB）
- [ ] 多文件处理（>10个文件）
- [ ] 内存使用监控
- [ ] 响应时间测试

### 用户界面测试
- [ ] 菜单项显示
- [ ] 对话框布局
- [ ] 错误消息显示
- [ ] 状态栏更新
- [ ] 进度指示

## 测试报告模板

### 测试结果记录

**测试日期**：_______
**测试人员**：_______
**测试环境**：_______

| 测试用例 | 状态 | 备注 |
|---------|------|------|
| 自动检测 | ✓/✗ |      |
| 手动连接 | ✓/✗ |      |
| Excel浏览器 | ✓/✗ |      |
| 数据读取 | ✓/✗ |      |
| 多文件处理 | ✓/✗ |      |
| 错误处理 | ✓/✗ |      |
| 性能测试 | ✓/✗ |      |

**发现的问题**：
1. _______
2. _______
3. _______

**改进建议**：
1. _______
2. _______
3. _______

**总体评价**：_______
