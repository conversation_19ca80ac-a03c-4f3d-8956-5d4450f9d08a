object frmMain: TfrmMain
  Left = 0
  Top = 0
  Caption = 'SQLite'#25968#25454#24211#24037#20855
  ClientHeight = 600
  ClientWidth = 900
  Color = clBtnFace
  Font.Charset = DEFAULT_CHARSET
  Font.Color = clWindowText
  Font.Height = -12
  Font.Name = 'Segoe UI'
  Font.Style = []
  Menu = mmMain
  Position = poScreenCenter
  OnCreate = FormCreate
  OnDestroy = FormDestroy
  TextHeight = 15
  object pnlMain: TPanel
    Left = 0
    Top = 26
    Width = 900
    Height = 555
    Align = alClient
    BevelOuter = bvNone
    TabOrder = 0
    object splVertical: TSplitter
      Left = 250
      Top = 0
      Height = 555
      ExplicitLeft = 200
      ExplicitTop = 232
      ExplicitHeight = 100
    end
    object pnlLeft: TPanel
      Left = 0
      Top = 0
      Width = 250
      Height = 555
      Align = alLeft
      BevelOuter = bvNone
      TabOrder = 0
      object tvDatabases: TTreeView
        Left = 0
        Top = 0
        Width = 250
        Height = 555
        Align = alClient
        Images = ilMain
        Indent = 19
        ReadOnly = True
        TabOrder = 0
        OnDblClick = tvDatabasesDblClick
      end
    end
    object pcRight: TPageControl
      Left = 253
      Top = 0
      Width = 647
      Height = 555
      ActivePage = tsData
      Align = alClient
      TabOrder = 1
      object tsData: TTabSheet
        Caption = #25968#25454
        object dbgData: TDBGrid
          Left = 0
          Top = 0
          Width = 639
          Height = 525
          Align = alClient
          Enabled = False
          Options = [dgTitles, dgIndicator, dgColumnResize, dgColLines, dgRowLines, dgTabs, dgConfirmDelete, dgCancelOnExit, dgTitleClick, dgTitleHotTrack]
          TabOrder = 0
          TitleFont.Charset = DEFAULT_CHARSET
          TitleFont.Color = clWindowText
          TitleFont.Height = -12
          TitleFont.Name = 'Segoe UI'
          TitleFont.Style = []
          Visible = False
        end
      end
    end
  end
  object sbMain: TStatusBar
    Left = 0
    Top = 581
    Width = 900
    Height = 19
    Panels = <>
    SimplePanel = True
    SimpleText = #26410#36830#25509
  end
  object tbMain: TToolBar
    Left = 0
    Top = 0
    Width = 900
    Height = 26
    Caption = 'tbMain'
    Images = ilMain
    List = True
    ParentShowHint = False
    ShowHint = True
    TabOrder = 2
    object btnConnect: TToolButton
      Left = 0
      Top = 0
      Action = actConnect
    end
    object btnDisconnect: TToolButton
      Left = 23
      Top = 0
      Action = actDisconnect
    end
    object btnExportData: TToolButton
      Left = 46
      Top = 0
      Action = actExportData
    end
    object btnImportData: TToolButton
      Left = 69
      Top = 0
      Action = actImportData
    end
    object btnRefreshData: TToolButton
      Left = 92
      Top = 0
      Action = actRefreshData
    end
    object btnModifyData: TToolButton
      Left = 115
      Top = 0
      Action = actModifyData
    end
    object btnRefreshDB: TToolButton
      Left = 138
      Top = 0
      Action = actRefreshDB
    end
    object btnSearchDB: TToolButton
      Left = 161
      Top = 0
      Action = actSearchDB
    end
    object btnConvertDB: TToolButton
      Left = 184
      Top = 0
      Action = actConvertDB
    end
    object BitBtn1: TBitBtn
      Left = 207
      Top = 0
      Width = 75
      Height = 22
      Caption = 'BitBtn1'
      Glyph.Data = {
        36050000424D3605000000000000360400002800000010000000100000000100
        08000000000000010000230B0000230B00000001000000000000000000000000
        80000080000000808000800000008000800080800000C0C0C000C0DCC000F0CA
        A6000020400000206000002080000020A0000020C0000020E000004000000040
        20000040400000406000004080000040A0000040C0000040E000006000000060
        20000060400000606000006080000060A0000060C0000060E000008000000080
        20000080400000806000008080000080A0000080C0000080E00000A0000000A0
        200000A0400000A0600000A0800000A0A00000A0C00000A0E00000C0000000C0
        200000C0400000C0600000C0800000C0A00000C0C00000C0E00000E0000000E0
        200000E0400000E0600000E0800000E0A00000E0C00000E0E000400000004000
        20004000400040006000400080004000A0004000C0004000E000402000004020
        20004020400040206000402080004020A0004020C0004020E000404000004040
        20004040400040406000404080004040A0004040C0004040E000406000004060
        20004060400040606000406080004060A0004060C0004060E000408000004080
        20004080400040806000408080004080A0004080C0004080E00040A0000040A0
        200040A0400040A0600040A0800040A0A00040A0C00040A0E00040C0000040C0
        200040C0400040C0600040C0800040C0A00040C0C00040C0E00040E0000040E0
        200040E0400040E0600040E0800040E0A00040E0C00040E0E000800000008000
        20008000400080006000800080008000A0008000C0008000E000802000008020
        20008020400080206000802080008020A0008020C0008020E000804000008040
        20008040400080406000804080008040A0008040C0008040E000806000008060
        20008060400080606000806080008060A0008060C0008060E000808000008080
        20008080400080806000808080008080A0008080C0008080E00080A0000080A0
        200080A0400080A0600080A0800080A0A00080A0C00080A0E00080C0000080C0
        200080C0400080C0600080C0800080C0A00080C0C00080C0E00080E0000080E0
        200080E0400080E0600080E0800080E0A00080E0C00080E0E000C0000000C000
        2000C0004000C0006000C0008000C000A000C000C000C000E000C0200000C020
        2000C0204000C0206000C0208000C020A000C020C000C020E000C0400000C040
        2000C0404000C0406000C0408000C040A000C040C000C040E000C0600000C060
        2000C0604000C0606000C0608000C060A000C060C000C060E000C0800000C080
        2000C0804000C0806000C0808000C080A000C080C000C080E000C0A00000C0A0
        2000C0A04000C0A06000C0A08000C0A0A000C0A0C000C0A0E000C0C00000C0C0
        2000C0C04000C0C06000C0C08000C0C0A000F0FBFF00A4A0A000808080000000
        FF0000FF000000FFFF00FF000000FF00FF00FFFF0000FFFFFF00070707070707
        0707070707070707070707070707070707070707070707070707070707070707
        EDED07070707070707070707070707E3E307070707070707070707070707E3E3
        ED070707070707070707070707EDE3E307070707070707070707070707EBE207
        07070707070707070707070707E3DA070707E2DADADA9A910707070709E39A07
        07070709ECE3E29A0707070709E2DA07070709E3ECEBE3DA0707070709E29A07
        07EDDADAEBECECDA0707070709E3DAE3E3DADAEBEC0909DA070707070909E2DA
        DAE2EBEC090707E2070707070709090909090909070707070707070707070909
        0909070707070707070707070707070707070707070707070707}
      TabOrder = 0
    end
  end
  object mmMain: TMainMenu
    Left = 48
    Top = 72
    object miFile: TMenuItem
      Caption = #25991#20214'(&F)'
      object miConnect: TMenuItem
        Action = actConnect
      end
      object miDisconnect: TMenuItem
        Action = actDisconnect
      end
      object N1: TMenuItem
        Caption = '-'
      end
      object miExcelBrowser: TMenuItem
        Action = actExcelBrowser
      end
      object N2: TMenuItem
        Caption = '-'
      end
      object miExit: TMenuItem
        Action = actExit
      end
    end
    object miEdit: TMenuItem
      Caption = #32534#36753'(&E)'
      object miConfig: TMenuItem
        Action = actConfig
      end
    end
    object miHelp: TMenuItem
      Caption = #24110#21161'(&H)'
      object miAbout: TMenuItem
        Caption = #20851#20110'(&A)...'
      end
      object miTestFormula: TMenuItem
        Caption = #27979#35797#20844#24335#35745#31639'(&T)...'
        OnClick = miTestFormulaClick
      end
    end
  end
  object ilMain: TImageList
    ColorDepth = cd32Bit
    Left = 48
    Top = 128
  end
  object alMain: TActionList
    Images = ilMain
    Left = 48
    Top = 184
    object actConnect: TAction
      Caption = #36830#25509'(&C)'
      Hint = #36830#25509#21040#25968#25454#24211
      OnExecute = actConnectExecute
    end
    object actDisconnect: TAction
      Caption = #26029#24320#36830#25509'(&D)'
      Hint = #26029#24320#25968#25454#24211#36830#25509
      OnExecute = actDisconnectExecute
    end
    object actExit: TAction
      Caption = #36864#20986'(&X)'
      Hint = #36864#20986#31243#24207
      OnExecute = actExitExecute
    end
    object actExportData: TAction
      Caption = #23548#20986#25968#25454'(&E)'
      Hint = #23548#20986#34920#25968#25454#21040#25991#20214
      ImageIndex = 0
      OnExecute = actExportDataExecute
    end
    object actImportData: TAction
      Caption = #23548#20837#25968#25454'(&I)'
      Hint = #20174#25991#20214#23548#20837#25968#25454
      ImageIndex = 1
      OnExecute = actImportDataExecute
    end
    object actRefreshData: TAction
      Caption = #21047#26032#25968#25454'(&R)'
      Hint = #21047#26032#24403#21069#34920#25968#25454
      OnExecute = actRefreshDataExecute
    end
    object actModifyData: TAction
      Caption = #20462#25913#25968#25454'(&M)'
      Hint = #20462#25913#34920#25968#25454
      OnExecute = actModifyDataExecute
    end
    object actRefreshDB: TAction
      Caption = #21047#26032#25968#25454#24211'(&F)'
      Hint = #21047#26032#25968#25454#24211#32467#26500
      OnExecute = actRefreshDBExecute
    end
    object actSearchDB: TAction
      Caption = #25628#32034#25968#25454#24211'(&S)'
      Hint = #22312#25968#25454#24211#20013#25628#32034#20869#23481
      ImageIndex = 2
      OnExecute = actSearchDBExecute
    end
    object actConvertDB: TAction
      Caption = #36716#25442#25968#25454#24211'(&C)'
      Hint = #36716#25442#25968#25454#24211#26684#24335
      ImageIndex = 3
      OnExecute = actConvertDBExecute
    end
    object actConfig: TAction
      Caption = #37197#32622#35774#32622'(&S)...'
      Hint = #25171#24320#37197#32622#35774#32622#31383#21475
      OnExecute = actConfigExecute
    end
    object actExcelBrowser: TAction
      Caption = 'Excel'#25968#25454#24211#27983#35272#22120'(&X)...'
      Hint = #25171#24320'Excel'#25968#25454#24211#27983#35272#22120
      OnExecute = actExcelBrowserExecute
    end
    object actSaveData: TAction
      Caption = #20445#23384#25968#25454'(&S)'
      Hint = #20445#23384#24403#21069#25968#25454#21040#25968#25454#24211
      ShortCut = 16467
    end
  end
  object dsData: TDataSource
    Left = 120
    Top = 184
  end
  object pmTreeView: TPopupMenu
    Left = 120
    Top = 72
    object miExportData: TMenuItem
      Action = actExportData
    end
    object miImportData: TMenuItem
      Action = actImportData
    end
    object miRefreshData: TMenuItem
      Action = actRefreshData
    end
    object miModifyData: TMenuItem
      Action = actModifyData
    end
    object miRefreshDB: TMenuItem
      Action = actRefreshDB
    end
    object miSearchDB: TMenuItem
      Action = actSearchDB
    end
    object miConvertDB: TMenuItem
      Action = actConvertDB
    end
    object miSaveData: TMenuItem
      Action = actSaveData
    end
  end
  object pmDBGrid: TPopupMenu
    Left = 120
    Top = 128
  end
end
