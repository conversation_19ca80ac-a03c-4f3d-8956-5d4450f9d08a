﻿unit UnifiedExcelProcessor_POI;

interface

uses
  System.Classes, System.SysUtils, System.Math, System.Variants, Vcl.Dialogs,
  FireDAC.Comp.Client, FireDAC.Stan.Def, FireDAC.Stan.Param,
  FireDAC.DatS, FireDAC.DApt.Intf, FireDAC.DApt, FireDAC.Comp.DataSet,
  Data.DB;

type
  TCellDataType = (cdtString, cdtNumber, cdtEmpty);

  // BIFF记录类型枚举
  TBIFFRecordType = (
    brtBOF = $0809,      // Beginning of File
    brtEOF = $000A,      // End of File
    brtSST = $00FC,      // Shared String Table
    brtLABEL_SST = $00FD, // Label with SST index
    brtNUMBER = $0203,   // Number
    brtRK = $027E,       // RK (compressed number)
    brtBLANK = $0201,    // Blank cell
    brtCONTINUE = $003C  // Continue record
  );

  // 单元格数据
  TExcelCell = class
  public
    Row, Col: Integer;
    Value: string;
    DataType: TCellDataType;
    constructor Create(ARow, ACol: Integer; const AValue: string; ADataType: TCellDataType = cdtString);
  end;

  // BIFF记录写入器
  TBIFFRecordWriter = class
  private
    FStream: TStream;
    FOwnsStream: Boolean;

    procedure WriteWord(Value: Word);
    procedure WriteDWord(Value: LongWord);
    procedure WriteBytes(const Data: TBytes);
    procedure WriteString(const Str: string; Unicode: Boolean = False);

  public
    constructor Create(AStream: TStream; AOwnsStream: Boolean = False);
    destructor Destroy; override;

    procedure WriteRecord(RecordType: TBIFFRecordType; const Data: TBytes);
    procedure WriteBOFRecord(BOFType: Word = $0005); // Workbook BOF
    procedure WriteEOFRecord;
    procedure WriteSSTRecord(const StringTable: TStringList);
    procedure WriteLabelSSTRecord(Row, Col: Word; SSTIndex: LongWord);
    procedure WriteNumberRecord(Row, Col: Word; Value: Double);
    procedure WriteRKRecord(Row, Col: Word; Value: Double);
    procedure WriteBlankRecord(Row, Col: Word);
  end;

  // Excel工作表
  TExcelSheet = class
  private
    FCells: TList;
    FName: string;
    FRowCount, FColCount: Integer;

    function FindCell(Row, Col: Integer): TExcelCell;

  public
    constructor Create(const AName: string = 'Sheet1');
    destructor Destroy; override;

    procedure AddCell(Row, Col: Integer; const Value: string; DataType: TCellDataType = cdtString);
    function GetCellValue(Row, Col: Integer): string;
    function HasData: Boolean;
    function ToDataSet: TFDMemTable;

    property Name: string read FName write FName;
    property RowCount: Integer read FRowCount;
    property ColCount: Integer read FColCount;
  end;

  // Excel工作簿
  TExcelWorkbook = class
  private
    FSheets: TList;
    FFileName: string;

  public
    constructor Create;
    destructor Destroy; override;

    function AddSheet(const Name: string = ''): TExcelSheet;
    function GetSheet(Index: Integer): TExcelSheet; overload;
    function GetSheetCount: Integer;
    function GetSheetNames: TStringList;
    procedure ClearSheets;

    property FileName: string read FFileName write FFileName;
  end;

  // POI式的统一Excel处理器
  TUnifiedExcelProcessor = class
  private
    FWorkbook: TExcelWorkbook;

    // POI式的核心方法
    function ReadNextBiffRecord(Stream: TStream; out RecordType: Word; out RecordLength: LongWord; out RecordData: TBytes): Boolean;
    function ProcessBiffRecord(RecordType: Word; const RecordData: TBytes; Sheet: TExcelSheet; StringTable: TStringList): Integer;
    function ParseSSTRecord(Stream: TStream; RecordLength: Word; StringTable: TStringList): Boolean;
    function ParseSSTRecordFromData(const RecordData: TBytes; StringTable: TStringList): Boolean;
    function ParseSSTRecordAdvanced(const MainRecord: TBytes; const ContinueRecords: array of TBytes; StringTable: TStringList): Boolean;
    function DecodeRKValue(B1, B2, B3, B4: Byte): string;
    function FindBiffStart(Stream: TStream): Boolean;
    function TryParseBiffData(Stream: TStream): Boolean;

  public
    constructor Create;
    destructor Destroy; override;

    // 读取功能
    function LoadFromFile(const FileName: string): Boolean;
    function LoadFromStream(Stream: TStream): Boolean;

    // 访问功能
    function GetSheetCount: Integer;
    function GetSheet(Index: Integer): TExcelSheet; overload;
    function GetSheetNames: TStringList;

    // 转换功能
    function SheetToDataSet(SheetIndex: Integer = 0): TFDMemTable;

    // 工具功能
    function IsValidExcelFile(const FileName: string): Boolean;

    // 写入功能
    function SaveToFile(const FileName: string): Boolean;
    function SaveToStream(Stream: TStream): Boolean;
    function WriteDataSetToSheet(DataSet: TDataSet; const SheetName: string = 'Sheet1'): Boolean;

    property Workbook: TExcelWorkbook read FWorkbook;
  end;

  // 简化的API接口
  TExcelAPI = class
  public
    // 读取功能
    class function ReadExcelFile(const FileName: string): string;
    class function ReadSheetToDataSet(const FileName: string; SheetIndex: Integer = 0): TFDMemTable; overload;
    class function ReadSheetToDataSet(const FileName: string; const SheetName: string): TFDMemTable; overload;
    class function GetSheetNames(const FileName: string): TStringList;
    class function IsValidExcelFile(const FileName: string): Boolean;
    class function GetExcelFileInfo(const FileName: string): string;

    // 写入功能
    class function WriteDataSetToExcel(DataSet: TDataSet; const FileName: string; const SheetName: string = 'Sheet1'): Boolean;
  end;

implementation

// TBIFFRecordWriter实现
constructor TBIFFRecordWriter.Create(AStream: TStream; AOwnsStream: Boolean);
begin
  inherited Create;
  FStream := AStream;
  FOwnsStream := AOwnsStream;
end;

destructor TBIFFRecordWriter.Destroy;
begin
  if FOwnsStream and Assigned(FStream) then
    FStream.Free;
  inherited;
end;

procedure TBIFFRecordWriter.WriteWord(Value: Word);
begin
  FStream.WriteBuffer(Value, SizeOf(Word));
end;

procedure TBIFFRecordWriter.WriteDWord(Value: LongWord);
begin
  FStream.WriteBuffer(Value, SizeOf(LongWord));
end;

procedure TBIFFRecordWriter.WriteBytes(const Data: TBytes);
begin
  if Length(Data) > 0 then
    FStream.WriteBuffer(Data[0], Length(Data));
end;

procedure TBIFFRecordWriter.WriteString(const Str: string; Unicode: Boolean);
var
  Data: TBytes;
  I: Integer;
begin
  if Unicode then
  begin
    // Unicode字符串 (16位)
    SetLength(Data, Length(Str) * 2);
    for I := 1 to Length(Str) do
    begin
      Data[(I-1)*2] := Ord(Str[I]) and $FF;
      Data[(I-1)*2+1] := (Ord(Str[I]) shr 8) and $FF;
    end;
  end
  else
  begin
    // ANSI字符串 (8位)
    SetLength(Data, Length(Str));
    for I := 1 to Length(Str) do
      Data[I-1] := Ord(Str[I]) and $FF;
  end;
  WriteBytes(Data);
end;

procedure TBIFFRecordWriter.WriteRecord(RecordType: TBIFFRecordType; const Data: TBytes);
begin
  WriteWord(Word(RecordType));
  WriteWord(Length(Data));
  WriteBytes(Data);
end;

procedure TBIFFRecordWriter.WriteBOFRecord(BOFType: Word);
var
  Data: TBytes;
begin
  SetLength(Data, 8);
  // BOF类型
  Data[0] := BOFType and $FF;
  Data[1] := (BOFType shr 8) and $FF;
  // BIFF版本 (BIFF5)
  Data[2] := $05;
  Data[3] := $00;
  // 年份 (当前年份)
  Data[4] := $C1; // 1993 (0x07C1)
  Data[5] := $07;
  // 历史标志
  Data[6] := $00;
  Data[7] := $00;
  WriteRecord(brtBOF, Data);
end;

procedure TBIFFRecordWriter.WriteEOFRecord;
var
  Data: TBytes;
begin
  SetLength(Data, 0);
  WriteRecord(brtEOF, Data);
end;

procedure TBIFFRecordWriter.WriteSSTRecord(const StringTable: TStringList);
var
  Data: TBytes;
  I, J, Pos: Integer;
  StrLen: Word;
  StrData: TBytes;
  TotalSize: Integer;
begin
  // 计算总大小
  TotalSize := 8; // 头部8字节
  for I := 0 to StringTable.Count - 1 do
  begin
    TotalSize := TotalSize + 3; // 长度(2) + 选项(1)
    TotalSize := TotalSize + Length(StringTable[I]); // 字符串数据
  end;

  SetLength(Data, TotalSize);

  // 写入SST头部
  // 总字符串数
  Data[0] := StringTable.Count and $FF;
  Data[1] := (StringTable.Count shr 8) and $FF;
  Data[2] := (StringTable.Count shr 16) and $FF;
  Data[3] := (StringTable.Count shr 24) and $FF;
  // 唯一字符串数
  Data[4] := StringTable.Count and $FF;
  Data[5] := (StringTable.Count shr 8) and $FF;
  Data[6] := (StringTable.Count shr 16) and $FF;
  Data[7] := (StringTable.Count shr 24) and $FF;

  Pos := 8;

  // 写入字符串
  for I := 0 to StringTable.Count - 1 do
  begin
    StrLen := Length(StringTable[I]);

    // 字符串长度
    Data[Pos] := StrLen and $FF;
    Data[Pos + 1] := (StrLen shr 8) and $FF;
    Inc(Pos, 2);

    // 选项字节 (0 = 8位字符串)
    Data[Pos] := $00;
    Inc(Pos);

    // 字符串数据
    SetLength(StrData, StrLen);
    for J := 1 to StrLen do
      StrData[J-1] := Ord(StringTable[I][J]) and $FF;

    Move(StrData[0], Data[Pos], StrLen);
    Inc(Pos, StrLen);
  end;

  WriteRecord(brtSST, Data);
end;

procedure TBIFFRecordWriter.WriteLabelSSTRecord(Row, Col: Word; SSTIndex: LongWord);
var
  Data: TBytes;
begin
  SetLength(Data, 10);

  // 行号
  Data[0] := Row and $FF;
  Data[1] := (Row shr 8) and $FF;

  // 列号
  Data[2] := Col and $FF;
  Data[3] := (Col shr 8) and $FF;

  // XF索引 (格式索引，使用默认值0)
  Data[4] := $00;
  Data[5] := $00;

  // SST索引
  Data[6] := SSTIndex and $FF;
  Data[7] := (SSTIndex shr 8) and $FF;
  Data[8] := (SSTIndex shr 16) and $FF;
  Data[9] := (SSTIndex shr 24) and $FF;

  WriteRecord(brtLABEL_SST, Data);
end;

procedure TBIFFRecordWriter.WriteNumberRecord(Row, Col: Word; Value: Double);
var
  Data: TBytes;
begin
  SetLength(Data, 14);

  // 行号
  Data[0] := Row and $FF;
  Data[1] := (Row shr 8) and $FF;

  // 列号
  Data[2] := Col and $FF;
  Data[3] := (Col shr 8) and $FF;

  // XF索引 (格式索引，使用默认值0)
  Data[4] := $00;
  Data[5] := $00;

  // 双精度浮点数值
  Move(Value, Data[6], SizeOf(Double));

  WriteRecord(brtNUMBER, Data);
end;

procedure TBIFFRecordWriter.WriteRKRecord(Row, Col: Word; Value: Double);
var
  Data: TBytes;
  RKValue: LongWord;
  IntValue: Integer;
  DoubleBytes: array[0..7] of Byte;
begin
  SetLength(Data, 10);

  // 行号
  Data[0] := Row and $FF;
  Data[1] := (Row shr 8) and $FF;

  // 列号
  Data[2] := Col and $FF;
  Data[3] := (Col shr 8) and $FF;

  // XF索引 (格式索引，使用默认值0)
  Data[4] := $00;
  Data[5] := $00;

  // 尝试将Double转换为RK格式
  if (Frac(Value) = 0) and (Value >= -536870912) and (Value <= 536870911) then
  begin
    // 可以表示为30位整数
    IntValue := Trunc(Value);
    RKValue := (LongWord(IntValue) shl 2) or $02; // 设置整数标志
  end
  else
  begin
    // 使用IEEE 754双精度浮点数的高32位
    Move(Value, DoubleBytes, 8);
    RKValue := (DoubleBytes[4]) or
               (DoubleBytes[5] shl 8) or
               (DoubleBytes[6] shl 16) or
               (DoubleBytes[7] shl 24);
    RKValue := RKValue and $FFFFFFFC; // 清除最低2位
  end;

  // RK值
  Data[6] := RKValue and $FF;
  Data[7] := (RKValue shr 8) and $FF;
  Data[8] := (RKValue shr 16) and $FF;
  Data[9] := (RKValue shr 24) and $FF;

  WriteRecord(brtRK, Data);
end;

procedure TBIFFRecordWriter.WriteBlankRecord(Row, Col: Word);
var
  Data: TBytes;
begin
  SetLength(Data, 6);

  // 行号
  Data[0] := Row and $FF;
  Data[1] := (Row shr 8) and $FF;

  // 列号
  Data[2] := Col and $FF;
  Data[3] := (Col shr 8) and $FF;

  // XF索引 (格式索引，使用默认值0)
  Data[4] := $00;
  Data[5] := $00;

  WriteRecord(brtBLANK, Data);
end;

// TExcelCell实现
constructor TExcelCell.Create(ARow, ACol: Integer; const AValue: string; ADataType: TCellDataType);
begin
  Row := ARow;
  Col := ACol;
  Value := AValue;
  DataType := ADataType;
end;

// TExcelSheet实现
constructor TExcelSheet.Create(const AName: string);
begin
  inherited Create;
  FCells := TList.Create;
  FName := AName;
  FRowCount := 0;
  FColCount := 0;
end;

destructor TExcelSheet.Destroy;
var
  I: Integer;
begin
  for I := 0 to FCells.Count - 1 do
    TExcelCell(FCells[I]).Free;
  FCells.Free;
  inherited;
end;

function TExcelSheet.FindCell(Row, Col: Integer): TExcelCell;
var
  I: Integer;
  Cell: TExcelCell;
begin
  Result := nil;
  for I := 0 to FCells.Count - 1 do
  begin
    Cell := TExcelCell(FCells[I]);
    if (Cell.Row = Row) and (Cell.Col = Col) then
    begin
      Result := Cell;
      Break;
    end;
  end;
end;

procedure TExcelSheet.AddCell(Row, Col: Integer; const Value: string; DataType: TCellDataType);
var
  Cell: TExcelCell;
begin
  // 调试：跟踪单元格添加


  Cell := FindCell(Row, Col);
  if Cell = nil then
  begin
    try
      Cell := TExcelCell.Create(Row, Col, Value, DataType);
      FCells.Add(Cell);
    except
      on E: Exception do
      begin
        ShowMessage('添加单元格失败[' + IntToStr(Row) + ',' + IntToStr(Col) + ']: ' + E.Message);
        Exit;
      end;
    end;
  end
  else
  begin
    Cell.Value := Value;
    Cell.DataType := DataType;
  end;

  // 更新范围
  if Row + 1 > FRowCount then FRowCount := Row + 1;
  if Col + 1 > FColCount then FColCount := Col + 1;
end;

function TExcelSheet.GetCellValue(Row, Col: Integer): string;
var
  Cell: TExcelCell;
begin
  Cell := FindCell(Row, Col);
  if Cell <> nil then
    Result := Cell.Value
  else
    Result := '';
end;

function TExcelSheet.HasData: Boolean;
begin
  Result := FCells.Count > 0;
end;

function TExcelSheet.ToDataSet: TFDMemTable;
var
  I, J, FieldIndex: Integer;
  Cell: TExcelCell;
  FieldName: string;
  HasData: Boolean;
  TestRow: Integer;
begin
  Result := TFDMemTable.Create(nil);

  try
    // 第一步：创建字段（基于第一行数据）
    FieldIndex := 0;
    for J := 0 to FColCount - 1 do
    begin
      // 检查这一列是否有数据
      HasData := False;
      for TestRow := 0 to FRowCount - 1 do
      begin
        Cell := FindCell(TestRow, J);
        if Assigned(Cell) and (Trim(Cell.Value) <> '') then
        begin
          HasData := True;
          Break;
        end;
      end;

      // 如果这一列有数据，创建字段
      if HasData then
      begin
        Cell := FindCell(0, J);
        if Assigned(Cell) and (Trim(Cell.Value) <> '') then
        begin
          FieldName := Trim(Cell.Value);
          // 只对字段名为"attr"的字段设置4000字符长度，其他字段增加到1000字符以支持更长数据
          if FieldName = 'attr' then
            Result.FieldDefs.Add(FieldName, ftWideString, 4000)
          else
            Result.FieldDefs.Add(FieldName, ftWideString, 1000);  // 从255增加到1000
        end;
      end;
    end;

    // 只有在有字段定义时才创建数据集
    if Result.FieldDefs.Count > 0 then
    begin
      Result.CreateDataSet;
      Result.Active := True;
    end
    else
    begin
      // 没有字段定义，创建一个默认字段
      Result.FieldDefs.Add('NoData', ftWideString, 255);
      Result.CreateDataSet;
      Result.Active := True;
      Exit; // 没有数据，直接返回空数据集
    end;

    // 第二步：添加数据（从第二行开始）
    for I := 1 to FRowCount - 1 do
    begin
      Result.Append;
      FieldIndex := 0;
      for J := 0 to FColCount - 1 do
      begin
        // 检查这一列是否有字段
        HasData := False;
        for TestRow := 0 to FRowCount - 1 do
        begin
          Cell := FindCell(TestRow, J);
          if Assigned(Cell) and (Trim(Cell.Value) <> '') then
          begin
            HasData := True;
            Break;
          end;
        end;

        // 如果这一列有字段，就填充数据
        if HasData then
        begin
          Cell := FindCell(I, J);
          if FieldIndex < Result.FieldCount then
          begin
            if Assigned(Cell) then
              Result.Fields[FieldIndex].AsString := Cell.Value
            else
              Result.Fields[FieldIndex].AsString := '';
          end;
          Inc(FieldIndex);
        end;
      end;
      Result.Post;
    end;

    Result.First;
  except
    Result.Free;
    raise;
  end;
end;

// TExcelWorkbook实现
constructor TExcelWorkbook.Create;
begin
  inherited Create;
  FSheets := TList.Create;
end;

destructor TExcelWorkbook.Destroy;
var
  I: Integer;
begin
  for I := 0 to FSheets.Count - 1 do
    TExcelSheet(FSheets[I]).Free;
  FSheets.Free;
  inherited;
end;

function TExcelWorkbook.AddSheet(const Name: string): TExcelSheet;
var
  SheetName: string;
begin
  if Name = '' then
    SheetName := 'Sheet' + IntToStr(FSheets.Count + 1)
  else
    SheetName := Name;

  Result := TExcelSheet.Create(SheetName);
  FSheets.Add(Result);
end;

function TExcelWorkbook.GetSheet(Index: Integer): TExcelSheet;
begin
  if (Index >= 0) and (Index < FSheets.Count) then
    Result := TExcelSheet(FSheets[Index])
  else
    Result := nil;
end;

function TExcelWorkbook.GetSheetCount: Integer;
begin
  Result := FSheets.Count;
end;

function TExcelWorkbook.GetSheetNames: TStringList;
var
  I: Integer;
begin
  Result := TStringList.Create;
  for I := 0 to FSheets.Count - 1 do
    Result.Add(TExcelSheet(FSheets[I]).Name);
end;

procedure TExcelWorkbook.ClearSheets;
var
  I: Integer;
begin
  for I := 0 to FSheets.Count - 1 do
    TExcelSheet(FSheets[I]).Free;
  FSheets.Clear;
end;

// TUnifiedExcelProcessor实现
constructor TUnifiedExcelProcessor.Create;
begin
  inherited Create;
  FWorkbook := TExcelWorkbook.Create;
end;

destructor TUnifiedExcelProcessor.Destroy;
begin
  FWorkbook.Free;
  inherited;
end;

// POI式的RecordInputStream - 负责底层记录读取和边界检查
function TUnifiedExcelProcessor.ReadNextBiffRecord(Stream: TStream; out RecordType: Word; out RecordLength: LongWord; out RecordData: TBytes): Boolean;
var
  Header: array[0..3] of Byte;
  MaxPos: Int64;
begin
  Result := False;
  MaxPos := Stream.Size;

  // 检查是否有足够的数据读取记录头
  if Stream.Position + 4 > MaxPos then
    Exit;

  // 读取记录头
  if Stream.Read(Header, 4) <> 4 then
    Exit;

  RecordType := Header[0] or (Header[1] shl 8);
  RecordLength := LongWord(Header[2] or (Header[3] shl 8));  // 正确转换为LongWord

  // POI式的记录长度验证 - 支持更大的记录，但BIFF记录长度实际最大65535
  // BIFF记录长度字段只有2字节，所以实际最大值是65535，这是正常的
  if (RecordLength > 65535) then
    Exit;

  // 检查是否有足够的数据读取记录内容
  if Stream.Position + RecordLength > MaxPos then
    Exit;

  // 读取记录数据
  SetLength(RecordData, RecordLength);
  if RecordLength > 0 then
  begin
    if Stream.Read(RecordData[0], RecordLength) <> RecordLength then
      Exit;
  end;

  Result := True;
end;

// 记录类型名称获取函数
function GetRecordTypeName(RecordType: Word): string;
begin
  case RecordType of
    $00FD: Result := 'LABEL_SST';
    $0204: Result := 'LABEL';
    $0203: Result := 'NUMBER';
    $027E: Result := 'RK';
    $0809: Result := 'BOF';
    $000A: Result := 'EOF';
    $00FC: Result := 'SST';
    else Result := 'UNKNOWN';
  end;
end;

// POI式的小端序读取函数
function ReadUShort(const Data: TBytes; Offset: Integer): Word;
begin
  if Offset + 1 < Length(Data) then
    Result := Data[Offset] or (Data[Offset + 1] shl 8)
  else
    Result := 0;
end;

function ReadInt(const Data: TBytes; Offset: Integer): LongWord;
begin
  if Offset + 3 < Length(Data) then
    Result := Data[Offset] or (Data[Offset + 1] shl 8) or (Data[Offset + 2] shl 16) or (Data[Offset + 3] shl 24)
  else
    Result := 0;
end;

// 读取LongWord值（用于行列号）
function ReadULong(const Data: TBytes; Offset: Integer): LongWord;
begin
  if Offset + 1 < Length(Data) then
    Result := Data[Offset] or (Data[Offset + 1] shl 8)
  else
    Result := 0;
end;

function ReadDouble(const Data: TBytes; Offset: Integer): Double;
var
  LongValue: Int64;
begin
  if Offset + 7 < Length(Data) then
  begin
    // POI式的小端序Long读取
    LongValue := Int64(Data[Offset]) or
                (Int64(Data[Offset + 1]) shl 8) or
                (Int64(Data[Offset + 2]) shl 16) or
                (Int64(Data[Offset + 3]) shl 24) or
                (Int64(Data[Offset + 4]) shl 32) or
                (Int64(Data[Offset + 5]) shl 40) or
                (Int64(Data[Offset + 6]) shl 48) or
                (Int64(Data[Offset + 7]) shl 56);
    // 转换为Double
    Move(LongValue, Result, SizeOf(Double));
  end
  else
    Result := 0.0;
end;

// POI式的记录处理器 - 负责解析具体的记录类型
function TUnifiedExcelProcessor.ProcessBiffRecord(RecordType: Word; const RecordData: TBytes; Sheet: TExcelSheet; StringTable: TStringList): Integer;
var
  CellRow, CellCol: LongWord;  // 从Word改为LongWord，支持更大的行列数
  SSTIndex: LongWord;
  CellValue: string;
  I, FirstCol, LastCol: LongWord;  // 从Word改为LongWord
  RKValue: LongWord;
  DoubleValue: Double;
  StringLength: LongWord;
  UnicodeFlag: Byte;
begin
  Result := 0; // 返回处理的单元格数量

  case RecordType of
    $00FD: // LABEL_SST - 使用共享字符串的文本单元格
    begin
      if Length(RecordData) >= 10 then
      begin
        // POI LabelSSTRecord: super(in) + readInt()
        CellRow := ReadULong(RecordData, 0);    // 行号
        CellCol := ReadULong(RecordData, 2);    // 列号
        // 跳过XF索引(偏移4-5)，SST索引在偏移6
        SSTIndex := ReadInt(RecordData, 6);      // SST索引

        // 调试信息
        //ShowMessage('LABEL_SST详细: 行=' + IntToStr(CellRow) + ', 列=' + IntToStr(CellCol) +
           //        ', SST索引=' + IntToStr(SSTIndex) + ', StringTable.Count=' + IntToStr(StringTable.Count));

        if (SSTIndex >= 0) and (SSTIndex < StringTable.Count) then
        begin
          try
            CellValue := StringTable[SSTIndex];

            // 特别关注索引590附近的字符串
            if (SSTIndex >= 585) and (SSTIndex <= 595) then
              ShowMessage('关键SST[' + IntToStr(SSTIndex) + '] 单元格[' + IntToStr(CellRow) + ',' + IntToStr(CellCol) + '] = "' + Copy(CellValue, 1, 50) + '"');

            // 如果字符串为空，使用占位符
            if CellValue = '' then
            begin
              CellValue := '[空字符串]';
              if (SSTIndex >= 585) and (SSTIndex <= 595) then
                ShowMessage('警告: SST[' + IntToStr(SSTIndex) + '] 是空字符串');
            end;

            Sheet.AddCell(CellRow, CellCol, CellValue, cdtString);
            Result := 1;
          except
            on E: Exception do
            begin
              ShowMessage('读取SST字符串[' + IntToStr(SSTIndex) + ']时出错: ' + E.Message);
              Sheet.AddCell(CellRow, CellCol, '[SST读取错误:' + IntToStr(SSTIndex) + ']', cdtString);
              Result := 1;
            end;
          end;
        end
        else
        begin
          // 超出范围的索引 - 这是关键问题
          if SSTIndex = StringTable.Count then
          begin
            // 正好超出1个，很可能是0-based vs 1-based的问题，或者SST解析不完整
            ShowMessage('关键问题: SST索引刚好超出1个: ' + IntToStr(SSTIndex) + ' >= ' + IntToStr(StringTable.Count) +
                        ' 单元格[' + IntToStr(CellRow) + ',' + IntToStr(CellCol) + ']' + #13#10 +
                        '这可能是SST解析不完整导致的！');
          end
          else
          begin
            ShowMessage('SST索引严重超出范围: ' + IntToStr(SSTIndex) + ' >= ' + IntToStr(StringTable.Count) + ' 单元格[' + IntToStr(CellRow) + ',' + IntToStr(CellCol) + ']');
          end;

          // 添加错误信息作为占位符，但仍然创建单元格
          Sheet.AddCell(CellRow, CellCol, '[SST索引' + IntToStr(SSTIndex) + '缺失]', cdtString);
          Result := 1;
        end;
      end;
    end;

    $0204: // LABEL - 直接文本单元格 (测试历史版本逻辑)
    begin
      if Length(RecordData) >= 8 then
      begin
        // 历史版本逻辑测试: Row(2) + Col(2) + XF(2) + StrLen(2) + 直接读取字符串
        CellRow := ReadULong(RecordData, 0);        // 行号
        CellCol := ReadULong(RecordData, 2);        // 列号
        // 跳过XF索引(偏移4-5)
        StringLength := ReadUShort(RecordData, 6);   // 字符串长度

        // 历史版本的条件检查
        if (StringLength > 0) and (StringLength < Length(RecordData) - 6) then
        begin
          // 完全模拟历史版本的SetString方式
          if 8 + StringLength <= Length(RecordData) then
          begin
            SetString(CellValue, PAnsiChar(@RecordData[8]), StringLength);
            CellValue := string(CellValue); // 确保字符串类型转换
            Sheet.AddCell(CellRow, CellCol, CellValue, cdtString);
            Result := 1;
          end;
        end;
      end;
    end;

    $0203: // NUMBER - 数字单元格
    begin
      if Length(RecordData) >= 14 then
      begin
        // POI NumberRecord: super(in) + readDouble()
        CellRow := ReadULong(RecordData, 0);        // 行号
        CellCol := ReadULong(RecordData, 2);        // 列号
        // 跳过XF索引(偏移4-5)，8字节双精度浮点数在偏移6处
        try
          DoubleValue := ReadDouble(RecordData, 6);  // POI式的Double读取

          // 调试信息
        //  ShowMessage('NUMBER记录: 行=' + IntToStr(CellRow) + ', 列=' + IntToStr(CellCol) +
         //            ', 原始Double=' + FloatToStr(DoubleValue));

          // 检查是否为有效数字
          if IsNan(DoubleValue) or IsInfinite(DoubleValue) then
            CellValue := '0'
          else if Abs(DoubleValue) < 1E-100 then
            CellValue := '0'
          else if Abs(DoubleValue) > 1E15 then
            CellValue := FloatToStrF(DoubleValue, ffExponent, 15, 2)
          else
            CellValue := FloatToStr(DoubleValue);

          //ShowMessage('NUMBER结果: "' + CellValue + '"');
        except
          on E: Exception do
          begin
            CellValue := '0';
           // ShowMessage('NUMBER解析异常: ' + E.Message);
          end;
        end;
        Sheet.AddCell(CellRow, CellCol, CellValue, cdtNumber);
        Result := 1;
      end;
    end;

    $027E: // RK - 压缩数字
    begin
      if Length(RecordData) >= 10 then
      begin
        // POI RKRecord: super(in) + 处理RK值
        CellRow := ReadULong(RecordData, 0);        // 行号
        CellCol := ReadULong(RecordData, 2);        // 列号
        // 跳过XF索引(偏移4-5)，RK值在偏移6
        RKValue := ReadInt(RecordData, 6);           // RK值

        // 调试信息
        //ShowMessage('RK记录: 行=' + IntToStr(CellRow) + ', 列=' + IntToStr(CellCol) +
         //          ', RK值=$' + IntToHex(RKValue, 8));

        CellValue := DecodeRKValue(RecordData[6], RecordData[7], RecordData[8], RecordData[9]);

        //ShowMessage('RK结果: "' + CellValue + '"');

        Sheet.AddCell(CellRow, CellCol, CellValue, cdtNumber);
        Result := 1;
      end;
    end;

    $00BD: // MulRK - 多个RK记录
    begin
      if Length(RecordData) >= 8 then
      begin
        CellRow := LongWord(RecordData[0] or (RecordData[1] shl 8));
        FirstCol := LongWord(RecordData[2] or (RecordData[3] shl 8));
        LastCol := LongWord(RecordData[Length(RecordData)-2] or (RecordData[Length(RecordData)-1] shl 8));

        I := 4; // 跳过行号和第一列号
        CellCol := FirstCol;

        while (I <= Length(RecordData) - 8) and (CellCol <= LastCol) do
        begin
          // 跳过XF索引(2字节)，读取RK值(4字节)
          CellValue := DecodeRKValue(RecordData[I+2], RecordData[I+3], RecordData[I+4], RecordData[I+5]);
          Sheet.AddCell(CellRow, CellCol, CellValue, cdtNumber);
          Inc(Result);
          Inc(CellCol);
          Inc(I, 6); // 2字节XF索引 + 4字节RK值
        end;
      end;
    end;

    $0201: // BLANK - 空单元格
    begin
      if Length(RecordData) >= 6 then
      begin
        CellRow := LongWord(RecordData[0] or (RecordData[1] shl 8));
        CellCol := LongWord(RecordData[2] or (RecordData[3] shl 8));
        Sheet.AddCell(CellRow, CellCol, '', cdtEmpty);
        Result := 1;
      end;
    end;
  end;
end;

// POI式的SST记录解析器（从RecordData解析）- 改进版，处理数据边界
function TUnifiedExcelProcessor.ParseSSTRecordFromData(const RecordData: TBytes; StringTable: TStringList): Boolean;
var
  TotalStrings, UniqueStrings: LongWord;
  I, Pos, K: Integer;
  StringLength: LongWord;
  StringValue: string;
  J: Integer;
  DataLength: Integer;
  IsValidString: Boolean;
  CharIndex, CharCode: Integer;
begin
  Result := False;
  DataLength := Length(RecordData);

  if DataLength < 8 then
    Exit;

  // 读取SST头部信息
  TotalStrings := RecordData[0] or (RecordData[1] shl 8) or (RecordData[2] shl 16) or (RecordData[3] shl 24);
  UniqueStrings := RecordData[4] or (RecordData[5] shl 8) or (RecordData[6] shl 16) or (RecordData[7] shl 24);

  Pos := 8; // 跳过头部

  // 解析字符串 - 改进的边界检查
  for I := 0 to UniqueStrings - 1 do
  begin
    // 检查是否有足够空间读取字符串长度
    if Pos + 2 > DataLength then
    begin
      // 为剩余的字符串添加占位符，保持索引连续性
      for K := I to UniqueStrings - 1 do
      begin
        StringTable.Add('[数据不足-索引' + IntToStr(K) + ']');
      end;
      Break;
    end;

    // 读取字符串长度
    StringLength := RecordData[Pos] or (RecordData[Pos + 1] shl 8);
    Inc(Pos, 2);



    // 检查字符串长度的合理性
    if StringLength > 32767 then // Word的最大值
    begin
      StringTable.Add('[长度异常]');
      Continue;
    end;

    // 特别处理索引589的问题
    if I = 589 then
    begin
      // 检查是否有足够的数据按最大可能长度读取
      var MaxPossibleLength := DataLength - Pos - 1; // 减去选项字节
      if StringLength > MaxPossibleLength then
      begin
        StringLength := MaxPossibleLength;
      end;
    end;

    // 检查是否有足够空间读取选项字节
    if Pos >= DataLength then
    begin
      // 为剩余的字符串添加占位符
      for K := I to UniqueStrings - 1 do
      begin
        StringTable.Add('[选项字节不足-索引' + IntToStr(K) + ']');
      end;
      Break;
    end;

    // 检查字符串选项字节
    var OptionByte := RecordData[Pos];

    // 特别调试索引588-592的选项字节
    if (I >= 588) and (I <= 592) then
      ShowMessage('SST[' + IntToStr(I) + '] 选项字节: $' + IntToHex(OptionByte, 2) +
                  ', 位置: ' + IntToStr(Pos) + '/' + IntToStr(DataLength) +
                  ', 字符串长度: ' + IntToStr(StringLength));

    if (OptionByte and $01) = 0 then
    begin
      // 8位字符串
      Inc(Pos); // 跳过选项字节

      // 检查是否有足够的数据
      var AvailableBytes := DataLength - Pos;
      if StringLength > AvailableBytes then
      begin
        if (I >= UniqueStrings - 10) or (I < 5) then
          ShowMessage('SST[' + IntToStr(I) + '] 8位字符串数据不足: 需要' + IntToStr(StringLength) + '字节，可用' + IntToStr(AvailableBytes) + '字节');
        StringLength := AvailableBytes; // 使用可用的字节数
      end;

      // 安全读取字符串数据
      StringValue := '';
      if StringLength > 0 then
      begin
        SetLength(StringValue, StringLength);
        for J := 0 to StringLength - 1 do
        begin
          StringValue[J + 1] := Chr(RecordData[Pos + J]);
        end;

        // 特别调试索引588-592附近的字符串内容
        if (I >= 588) and (I <= 592) then
        begin
          var HexStr := '';
          var MaxBytes := Min(StringLength, 20);
          for J := 0 to MaxBytes - 1 do
            HexStr := HexStr + IntToHex(RecordData[Pos + J], 2) + ' ';
          ShowMessage('SST[' + IntToStr(I) + '] 8位字符串解析:' + #13#10 +
                      '内容: "' + StringValue + '"' + #13#10 +
                      '长度: ' + IntToStr(StringLength) + ' 字节' + #13#10 +
                      '数据: ' + HexStr + #13#10 +
                      '解析后位置: ' + IntToStr(Pos + StringLength) + '/' + IntToStr(DataLength));
        end;
      end;
      Inc(Pos, StringLength);

      // 特别调试索引588的位置计算
      if I = 588 then
        ShowMessage('SST[588] 解析完成，新位置: ' + IntToStr(Pos) + '/' + IntToStr(DataLength));
    end
    else
    begin
      // 16位字符串 (Unicode)
      Inc(Pos); // 跳过选项字节

      // 检查是否有足够的数据（每个字符需要2字节）
      var AvailableBytes := DataLength - Pos;
      var MaxChars := AvailableBytes div 2;
      if StringLength > MaxChars then
      begin
        if (I >= UniqueStrings - 10) or (I < 5) then
          ShowMessage('SST[' + IntToStr(I) + '] 16位字符串数据不足: 需要' + IntToStr(StringLength) + '字符(' + IntToStr(StringLength*2) + '字节)，可用' + IntToStr(MaxChars) + '字符(' + IntToStr(AvailableBytes) + '字节)');
        StringLength := MaxChars; // 使用可用的字符数
      end;

      // 安全读取Unicode字符串数据
      StringValue := '';
      for J := 0 to StringLength - 1 do
      begin
        // 确保有足够的字节读取一个完整的Unicode字符
        if Pos + J * 2 + 1 < DataLength then
        begin
          var LowByte := RecordData[Pos + J * 2];
          var HighByte := RecordData[Pos + J * 2 + 1];
          var UnicodeChar := LowByte or (HighByte shl 8);

          // 对于基本ASCII范围，直接转换；对于扩展字符，保持原样
          if UnicodeChar < 256 then
            StringValue := StringValue + Chr(UnicodeChar)
          else
            StringValue := StringValue + WideChar(UnicodeChar);
        end
        else
        begin
          // 数据不足，停止读取
          if (I >= 585) and (I <= 595) then
            ShowMessage('SST[' + IntToStr(I) + '] 16位字符串在字符' + IntToStr(J) + '处数据不足');
          Break;
        end;
      end;

      // 特别调试索引588-592附近的16位字符串内容
      if (I >= 588) and (I <= 592) then
      begin
        var HexStr := '';
        var MaxBytes := Min(StringLength * 2, 40);
        for J := 0 to MaxBytes - 1 do
          HexStr := HexStr + IntToHex(RecordData[Pos + J], 2) + ' ';
        ShowMessage('SST[' + IntToStr(I) + '] 16位字符串解析:' + #13#10 +
                    '内容: "' + StringValue + '"' + #13#10 +
                    '长度: ' + IntToStr(StringLength) + ' 字符(' + IntToStr(StringLength*2) + '字节)' + #13#10 +
                    '数据: ' + HexStr + #13#10 +
                    '解析后位置: ' + IntToStr(Pos + StringLength * 2) + '/' + IntToStr(DataLength));
      end;

      Inc(Pos, StringLength * 2);
    end;

    // 验证字符串内容的合理性（特别针对索引589）
    if I = 589 then
    begin
      IsValidString := True;
      // 检查是否包含异常字符
      for CharIndex := 1 to Length(StringValue) do
      begin
        CharCode := Ord(StringValue[CharIndex]);
        if (CharCode < 32) and (CharCode <> 9) and (CharCode <> 10) and (CharCode <> 13) then
        begin
          IsValidString := False;
          Break;
        end;
      end;

      if not IsValidString then
      begin
        ShowMessage('索引589字符串包含异常字符，替换为安全内容');
        StringValue := '[索引589-内容异常]';
      end;

      ShowMessage('SST[589] 最终结果: "' + StringValue + '" (长度:' + IntToStr(Length(StringValue)) + ')');
    end;

    StringTable.Add(StringValue);
  end;

  Result := True;
end;

// POI式的SST记录解析器（从Stream解析，保留兼容性）
function TUnifiedExcelProcessor.ParseSSTRecord(Stream: TStream; RecordLength: Word; StringTable: TStringList): Boolean;
var
  Buffer: TBytes;
  TotalStrings, UniqueStrings: LongWord;
  I, Pos: Integer;
  StringLength: LongWord;
  StringValue: string;
  J: Integer;
begin
  Result := False;

  if RecordLength < 8 then
    Exit;

  SetLength(Buffer, RecordLength);
  if Stream.Read(Buffer[0], RecordLength) <> RecordLength then
    Exit;

  // 读取SST头部信息
  TotalStrings := Buffer[0] or (Buffer[1] shl 8) or (Buffer[2] shl 16) or (Buffer[3] shl 24);
  UniqueStrings := Buffer[4] or (Buffer[5] shl 8) or (Buffer[6] shl 16) or (Buffer[7] shl 24);

  Pos := 8; // 跳过头部

  // 解析字符串
  for I := 0 to UniqueStrings - 1 do
  begin
    // 特别调试索引588-590的循环开始位置
    if (I >= 588) and (I <= 590) then
      ShowMessage('开始解析SST[' + IntToStr(I) + '], 当前位置: ' + IntToStr(Pos) + '/' + IntToStr(RecordLength));
    if Pos + 2 > RecordLength then
      Break;

    // 读取字符串长度
    StringLength := Buffer[Pos] or (Buffer[Pos + 1] shl 8);
    Inc(Pos, 2);

    // 限制字符串长度以防止内存问题 - 支持更大的字符串
    if StringLength > 100000000 then  // 100MB字符串限制 (100M字符)
      StringLength := 100000000;

    if Pos + 1 > RecordLength then
      Break;

    // 检查字符串选项字节
    if (Buffer[Pos] and $01) = 0 then
    begin
      // 8位字符串
      Inc(Pos); // 跳过选项字节
      if Pos + StringLength > RecordLength then
        StringLength := RecordLength - Pos;

      StringValue := '';
      for J := 0 to StringLength - 1 do
      begin
        if Pos + J < RecordLength then
          StringValue := StringValue + Chr(Buffer[Pos + J]);
      end;
      Inc(Pos, StringLength);
    end
    else
    begin
      // 16位字符串
      Inc(Pos); // 跳过选项字节
      if Pos + StringLength * 2 > RecordLength then
        StringLength := (RecordLength - Pos) div 2;

      StringValue := '';
      for J := 0 to StringLength - 1 do
      begin
        if Pos + J * 2 + 1 < RecordLength then
          StringValue := StringValue + Chr(Buffer[Pos + J * 2]);
      end;
      Inc(Pos, StringLength * 2);
    end;

    StringTable.Add(StringValue);
  end;

  Result := True;
end;

// POI式的RK值解码器 - 修复版本
function TUnifiedExcelProcessor.DecodeRKValue(B1, B2, B3, B4: Byte): string;
var
  RKValue: LongWord;
  IntValue: Integer;
  FloatValue: Double;
  Multiplied: Boolean;
  IsInteger: Boolean;
  DoubleBytes: array[0..7] of Byte;
begin
  RKValue := B1 or (B2 shl 8) or (B3 shl 16) or (B4 shl 24);

  Multiplied := (RKValue and $01) <> 0;
  IsInteger := (RKValue and $02) <> 0;

  // 调试信息
  //ShowMessage('RK解码: RK=$' + IntToHex(RKValue, 8) +
  //           ', Multiplied=' + BoolToStr(Multiplied, True) +
   //          ', IsInteger=' + BoolToStr(IsInteger, True));

  if IsInteger then
  begin
    // 整数值 - 30位有符号整数
    IntValue := Integer(RKValue shr 2);
    if Multiplied then
      Result := FloatToStr(IntValue / 100.0)
    else
      Result := IntToStr(IntValue);
  end
  else
  begin
    // 浮点数值 - RK值是IEEE 754双精度浮点数的高32位
    // 需要构造完整的64位双精度浮点数
    FillChar(DoubleBytes, 8, 0);

    // RK值去掉最低2位后作为双精度浮点数的高32位
    var HighPart := RKValue and $FFFFFFFC;
    DoubleBytes[4] := (HighPart) and $FF;
    DoubleBytes[5] := (HighPart shr 8) and $FF;
    DoubleBytes[6] := (HighPart shr 16) and $FF;
    DoubleBytes[7] := (HighPart shr 24) and $FF;

    Move(DoubleBytes, FloatValue, 8);

    if Multiplied then
      FloatValue := FloatValue / 100.0;

  //  ShowMessage('RK浮点解码: HighPart=$' + IntToHex(HighPart, 8) + ', FloatValue=' + FloatToStr(FloatValue));

    // 检查是否为有效数字
    if IsNan(FloatValue) or IsInfinite(FloatValue) or (Abs(FloatValue) < 1E-100) then
      Result := '0'
    else
      Result := FloatToStr(FloatValue);
  end;

  //ShowMessage('RK最终结果: "' + Result + '"');
end;

// 历史版本的BIFF数据开始位置查找器 (正确的实现)
function TUnifiedExcelProcessor.FindBiffStart(Stream: TStream): Boolean;
var
  Buffer: array[0..7] of Byte;
  MaxSearch: Int64;
begin
  Result := False;
  Stream.Position := 0;
  MaxSearch := Min(Stream.Size, 4096); // 搜索前4KB

  // 寻找BIFF BOF记录 ($0809)
  while Stream.Position < MaxSearch - 4 do
  begin
    if Stream.Read(Buffer, 4) = 4 then
    begin
      if (Buffer[0] = $09) and (Buffer[1] = $08) then
      begin
        // 找到可能的BOF记录，回退到记录开始
        Stream.Position := Stream.Position - 4;
        Result := True;
        ShowMessage('找到BIFF开始位置: ' + IntToHex(Stream.Position, 8));
        Exit;
      end
      else
      begin
        // 回退3个字节，继续搜索
        Stream.Position := Stream.Position - 3;
      end;
    end;
  end;
end;

// POI式的主要BIFF数据解析流程
function TUnifiedExcelProcessor.TryParseBiffData(Stream: TStream): Boolean;
var
  RecordType: Word;
  RecordLength: LongWord;
  RecordData: TBytes;
  Sheet: TExcelSheet;
  StringTable: TStringList;
  RecordCount, CellRecordCount: Integer;
  ProcessedCells: Integer;
  HexStr: string;
  I: Integer;
  EndIdx, StartIdx: Integer;
  EndHex, StartHex: string;
begin
  Result := False;
  StringTable := TStringList.Create;

  try
    // 寻找BIFF数据的开始位置
    if not FindBiffStart(Stream) then
      Exit;

    // 创建默认工作表
    Sheet := FWorkbook.AddSheet('Sheet1');
    if not Assigned(Sheet) then
      Exit;

    RecordCount := 0;
    CellRecordCount := 0;

    // POI式的记录处理循环
    while ReadNextBiffRecord(Stream, RecordType, RecordLength, RecordData) do
    begin
      Inc(RecordCount);

      // 每1000个记录显示进度
      if RecordCount mod 1000 = 0 then
        ShowMessage('解析进度: 已处理 ' + IntToStr(RecordCount) + ' 个记录');

      // 处理SST记录和CONTINUE记录
      if RecordType = $00FC then
      begin
        // 主SST记录 - 需要收集所有CONTINUE记录
        var CompleteSST: TBytes;
        SetLength(CompleteSST, Length(RecordData));
        Move(RecordData[0], CompleteSST[0], Length(RecordData));

        ShowMessage('开始SST解析: 主记录长度=' + IntToStr(Length(RecordData)) + ' 字节');

        // 收集所有CONTINUE记录（参考Apache POI的处理方式）
        var ContinueCount := 0;
        var NextRecordType: Word;
        var NextRecordLength: LongWord;
        var NextRecordData: TBytes;
        var SavedPosition := Stream.Position;
        var ContinueRecords: array of TBytes; // 存储CONTINUE记录信息

        // 预读下一个记录，检查是否为CONTINUE
        while ReadNextBiffRecord(Stream, NextRecordType, NextRecordLength, NextRecordData) do
        begin
          if NextRecordType = $003C then // CONTINUE记录
          begin
            Inc(ContinueCount);

            // 重要：CONTINUE记录的第一个字节是压缩标志（参考POI源码）
            ShowMessage('CONTINUE记录#' + IntToStr(ContinueCount) + ': 长度=' + IntToStr(Length(NextRecordData)) +
                       ' 字节，第一字节(压缩标志)=$' + IntToHex(NextRecordData[0], 2));

            // 存储CONTINUE记录信息以便后续智能合并
            SetLength(ContinueRecords, ContinueCount);
            SetLength(ContinueRecords[ContinueCount-1], Length(NextRecordData));
            Move(NextRecordData[0], ContinueRecords[ContinueCount-1][0], Length(NextRecordData));

            // 智能合并：跳过CONTINUE记录的第一个字节（压缩标志）
            var OldLength := Length(CompleteSST);
            var DataToMerge := Length(NextRecordData) - 1; // 跳过第一个字节
            SetLength(CompleteSST, OldLength + DataToMerge);
            if DataToMerge > 0 then
              Move(NextRecordData[1], CompleteSST[OldLength], DataToMerge); // 从第二个字节开始复制

            ShowMessage('智能合并CONTINUE记录#' + IntToStr(ContinueCount) + ': +' + IntToStr(DataToMerge) + ' 字节(跳过压缩标志)，总长度=' + IntToStr(Length(CompleteSST)));
            SavedPosition := Stream.Position;
          end
          else
          begin
            // 不是CONTINUE记录，回退流位置
            Stream.Position := SavedPosition;
            Break;
          end;
        end;

        ShowMessage('SST记录合并完成: 主记录 + ' + IntToStr(ContinueCount) + ' 个CONTINUE记录，总长度=' + IntToStr(Length(CompleteSST)) + ' 字节');

        // 关键调试：检查记录边界附近的数据
        if ContinueCount > 0 then
        begin
          var MainRecordLength := Length(RecordData);
          ShowMessage('=== 记录边界分析 ===');
          ShowMessage('主记录长度: ' + IntToStr(MainRecordLength) + ' 字节');

          // 显示主记录结尾的最后10字节
          EndHex := '';
          for EndIdx := Max(0, MainRecordLength - 10) to MainRecordLength - 1 do
            EndHex := EndHex + IntToHex(CompleteSST[EndIdx], 2) + ' ';
          ShowMessage('主记录结尾10字节: ' + EndHex);

          // 显示CONTINUE记录开头的前10字节
          StartHex := '';
          for StartIdx := MainRecordLength to Min(MainRecordLength + 9, Length(CompleteSST) - 1) do
            StartHex := StartHex + IntToHex(CompleteSST[StartIdx], 2) + ' ';
          ShowMessage('CONTINUE开头10字节: ' + StartHex);
          ShowMessage('CONTINUE第一字节(压缩标志): $' + IntToHex(CompleteSST[MainRecordLength], 2));
        end;

        // 使用高级解析器处理复杂的CONTINUE记录场景
        if ContinueCount > 0 then
          ParseSSTRecordAdvanced(RecordData, ContinueRecords, StringTable)
        else
          ParseSSTRecordFromData(CompleteSST, StringTable);

        // 显示SST字符串表内容（前20个）
        ShowMessage('SST字符串表解析完成，共' + IntToStr(StringTable.Count) + '个字符串');
        var SSTIndex: Integer;
        var SSTContent: string := '';
        for SSTIndex := 0 to Min(19, StringTable.Count - 1) do
        begin
          SSTContent := SSTContent + '[' + IntToStr(SSTIndex) + '] = "' + StringTable[SSTIndex] + '"' + #13#10;
        end;
        if SSTContent <> '' then
          ShowMessage('SST字符串表内容:' + #13#10 + SSTContent);

        Continue;
      end;

      // 处理数据记录
      ProcessedCells := ProcessBiffRecord(RecordType, RecordData, Sheet, StringTable);
      Inc(CellRecordCount, ProcessedCells);

      // 调试：跟踪单元格记录处理
      if ProcessedCells > 0 then
      begin
        if CellRecordCount mod 100 = 0 then
          ShowMessage('单元格记录处理进度: 已处理 ' + IntToStr(CellRecordCount) + ' 个单元格记录，Sheet.FCells.Count = ' + IntToStr(Sheet.FCells.Count));
      end;

      // 调试：显示前10个数据记录的详细信息
      if (RecordType = $00FD) or (RecordType = $0204) or (RecordType = $0203) or (RecordType = $027E) then
      begin
        if CellRecordCount <= 10 then
        begin
         // ShowMessage('数据记录#' + IntToStr(CellRecordCount) + ': 类型$' + IntToHex(RecordType, 4) +
            //         ' (' + GetRecordTypeName(RecordType) + ')' +
              //       ', 长度:' + IntToStr(RecordLength) + ', 处理单元格数: ' + IntToStr(ProcessedCells));

          // 显示前16个字节的原始数据
          if Length(RecordData) > 0 then
          begin
            HexStr := '原始数据: ';
            for I := 0 to Min(15, Length(RecordData) - 1) do
              HexStr := HexStr + IntToHex(RecordData[I], 2) + ' ';
            //ShowMessage(HexStr);

            // 解析并显示行列信息
            if Length(RecordData) >= 6 then
            begin
             // ShowMessage('解析: 行=' + IntToStr(ReadUShort(RecordData, 0)) +
               //          ', 列=' + IntToStr(ReadUShort(RecordData, 2)) +
                 //        ', XF=' + IntToStr(ReadUShort(RecordData, 4)));

              // 根据记录类型显示特定信息
            //  case RecordType of
             //   $00FD: ShowMessage('LABEL_SST: SST索引=' + IntToStr(ReadInt(RecordData, 6)));
             //   $0204: if Length(RecordData) >= 8 then
             //            ShowMessage('LABEL: 字符串长度=' + IntToStr(ReadUShort(RecordData, 6)) +
             //                      ', Unicode标志=' + IntToStr(RecordData[8]));
             //   $0203: ShowMessage('NUMBER: 数值=' + FloatToStr(ReadDouble(RecordData, 6)));
              //  $027E: ShowMessage('RK: RK值=' + IntToHex(ReadInt(RecordData, 6), 8));
             // end;
            end;
          end;
        end;
      end;
    end;

    // 显示详细的解析统计信息
    ShowMessage('=== 解析完成统计 ===' + #13#10 +
                '总记录数: ' + IntToStr(RecordCount) + #13#10 +
                '单元格记录数: ' + IntToStr(CellRecordCount) + #13#10 +
                'SST字符串表大小: ' + IntToStr(StringTable.Count) + #13#10 +
                '实际单元格数: ' + IntToStr(Sheet.FCells.Count) + #13#10 +
                '数据范围: ' + IntToStr(Sheet.FRowCount) + '行 × ' + IntToStr(Sheet.FColCount) + '列');

    Result := Sheet.HasData or (RecordCount > 10) or (StringTable.Count > 0);

  finally
    StringTable.Free;
  end;
end;

// 公共接口方法实现
function TUnifiedExcelProcessor.LoadFromFile(const FileName: string): Boolean;
var
  FileStream: TFileStream;
begin
  Result := False;

  if not FileExists(FileName) then
    Exit;

  try
    // 使用共享读取模式，允许其他程序同时访问文件
    FileStream := TFileStream.Create(FileName, fmOpenRead or fmShareDenyNone);
    try
      FWorkbook.FileName := FileName;
      FWorkbook.ClearSheets;
      Result := LoadFromStream(FileStream);
    finally
      FileStream.Free;
    end;
  except
    on E: Exception do
    begin
      ShowMessage('加载文件失败: ' + E.Message);
      Result := False;
    end;
  end;
end;

function TUnifiedExcelProcessor.LoadFromStream(Stream: TStream): Boolean;
begin
  Result := False;
  if not Assigned(Stream) or (Stream.Size = 0) then
    Exit;

  try
    FWorkbook.ClearSheets;
    Result := TryParseBiffData(Stream);
  except
    on E: Exception do
    begin
      ShowMessage('解析流异常: ' + E.Message);
      Result := False;
    end;
  end;
end;

function TUnifiedExcelProcessor.GetSheetCount: Integer;
begin
  Result := FWorkbook.GetSheetCount;
end;

function TUnifiedExcelProcessor.GetSheet(Index: Integer): TExcelSheet;
begin
  Result := FWorkbook.GetSheet(Index);
end;

function TUnifiedExcelProcessor.GetSheetNames: TStringList;
begin
  Result := FWorkbook.GetSheetNames;
end;

function TUnifiedExcelProcessor.SheetToDataSet(SheetIndex: Integer): TFDMemTable;
var
  Sheet: TExcelSheet;
begin
  Result := nil;
  Sheet := GetSheet(SheetIndex);
  if Assigned(Sheet) then
    Result := Sheet.ToDataSet;
end;

function TUnifiedExcelProcessor.IsValidExcelFile(const FileName: string): Boolean;
var
  FileStream: TFileStream;
  Buffer: array[0..7] of Byte;
begin
  Result := False;

  if not FileExists(FileName) then
    Exit;

  try
    // 使用共享读取模式，允许其他程序同时访问文件
    FileStream := TFileStream.Create(FileName, fmOpenRead or fmShareDenyNone);
    try
      if FileStream.Read(Buffer, 8) = 8 then
      begin
        // 检查OLE文件头
        Result := (Buffer[0] = $D0) and (Buffer[1] = $CF) and
                  (Buffer[2] = $11) and (Buffer[3] = $E0);
      end;
    finally
      FileStream.Free;
    end;
  except
    Result := False;
  end;
end;

// 写入功能实现
function TUnifiedExcelProcessor.SaveToFile(const FileName: string): Boolean;
var
  FileStream: TFileStream;
begin
  Result := False;
  try
    FileStream := TFileStream.Create(FileName, fmCreate);
    try
      Result := SaveToStream(FileStream);
    finally
      FileStream.Free;
    end;
  except
    on E: Exception do
    begin
      ShowMessage('保存文件失败: ' + E.Message);
      Result := False;
    end;
  end;
end;

function TUnifiedExcelProcessor.SaveToStream(Stream: TStream): Boolean;
var
  Writer: TBIFFRecordWriter;
  StringTable: TStringList;
  I, J, Row, Col: Integer;
  Sheet: TExcelSheet;
  Cell: TExcelCell;
  SSTIndex: Integer;
  CellValue: string;
  NumValue: Double;
begin
  Result := False;

  if not Assigned(FWorkbook) or (FWorkbook.GetSheetCount = 0) then
  begin
    ShowMessage('没有可写入的工作表数据');
    Exit;
  end;

  try
    Writer := TBIFFRecordWriter.Create(Stream, False);
    StringTable := TStringList.Create;
    try
      // 写入工作簿BOF
      Writer.WriteBOFRecord($0005); // Workbook BOF

      // 收集所有字符串到SST
      for I := 0 to FWorkbook.GetSheetCount - 1 do
      begin
        Sheet := FWorkbook.GetSheet(I);
        if Assigned(Sheet) then
        begin
          for J := 0 to Sheet.FCells.Count - 1 do
          begin
            Cell := TExcelCell(Sheet.FCells[J]);
            if (Cell.DataType = cdtString) and (StringTable.IndexOf(Cell.Value) = -1) then
              StringTable.Add(Cell.Value);
          end;
        end;
      end;

      // 写入SST记录
      if StringTable.Count > 0 then
        Writer.WriteSSTRecord(StringTable);

      // 写入每个工作表
      for I := 0 to FWorkbook.GetSheetCount - 1 do
      begin
        Sheet := FWorkbook.GetSheet(I);
        if Assigned(Sheet) then
        begin
          // 写入工作表BOF
          Writer.WriteBOFRecord($0010); // Worksheet BOF

          // 写入单元格数据
          for J := 0 to Sheet.FCells.Count - 1 do
          begin
            Cell := TExcelCell(Sheet.FCells[J]);
            Row := Cell.Row;
            Col := Cell.Col;

            case Cell.DataType of
              cdtString:
              begin
                SSTIndex := StringTable.IndexOf(Cell.Value);
                if SSTIndex >= 0 then
                  Writer.WriteLabelSSTRecord(Row, Col, SSTIndex)
                else
                  Writer.WriteBlankRecord(Row, Col);
              end;

              cdtNumber:
              begin
                CellValue := Cell.Value;
                if TryStrToFloat(CellValue, NumValue) then
                begin
                  // 对于小整数使用RK记录，其他使用NUMBER记录
                  if (Frac(NumValue) = 0) and (NumValue >= -536870912) and (NumValue <= 536870911) then
                    Writer.WriteRKRecord(Row, Col, NumValue)
                  else
                    Writer.WriteNumberRecord(Row, Col, NumValue);
                end
                else
                  Writer.WriteBlankRecord(Row, Col);
              end;

              cdtEmpty:
                Writer.WriteBlankRecord(Row, Col);
            end;
          end;

          // 写入工作表EOF
          Writer.WriteEOFRecord;
        end;
      end;

      // 写入工作簿EOF
      Writer.WriteEOFRecord;

      Result := True;

    finally
      StringTable.Free;
      Writer.Free;
    end;
  except
    on E: Exception do
    begin
      ShowMessage('写入流失败: ' + E.Message);
      Result := False;
    end;
  end;
end;

function TUnifiedExcelProcessor.WriteDataSetToSheet(DataSet: TDataSet; const SheetName: string): Boolean;
var
  Sheet: TExcelSheet;
  I, Row: Integer;
  FieldValue: string;
  NumValue: Double;
begin
  Result := False;

  if not Assigned(DataSet) or not DataSet.Active then
  begin
    ShowMessage('数据集无效或未激活');
    Exit;
  end;

  try
    // 清空现有工作簿
    FWorkbook.ClearSheets;

    // 创建新工作表
    Sheet := FWorkbook.AddSheet(SheetName);
    if not Assigned(Sheet) then
    begin
      ShowMessage('创建工作表失败');
      Exit;
    end;

    // 写入表头
    for I := 0 to DataSet.FieldCount - 1 do
    begin
      Sheet.AddCell(0, I, DataSet.Fields[I].FieldName, cdtString);
    end;

    // 写入数据行
    Row := 1;
    DataSet.First;
    while not DataSet.Eof do
    begin
      for I := 0 to DataSet.FieldCount - 1 do
      begin
        FieldValue := DataSet.Fields[I].AsString;

        // 判断数据类型
        if DataSet.Fields[I].DataType in [ftInteger, ftSmallint, ftWord, ftFloat, ftCurrency, ftBCD, ftFMTBcd] then
        begin
          // 数字字段
          if TryStrToFloat(FieldValue, NumValue) then
            Sheet.AddCell(Row, I, FieldValue, cdtNumber)
          else
            Sheet.AddCell(Row, I, FieldValue, cdtString);
        end
        else
        begin
          // 文本字段
          if FieldValue = '' then
            Sheet.AddCell(Row, I, '', cdtEmpty)
          else
            Sheet.AddCell(Row, I, FieldValue, cdtString);
        end;
      end;

      Inc(Row);
      DataSet.Next;
    end;

    Result := True;

  except
    on E: Exception do
    begin
      ShowMessage('写入数据集到工作表失败: ' + E.Message);
      Result := False;
    end;
  end;
end;

// TExcelAPI类实现 - 简化的静态接口
class function TExcelAPI.ReadExcelFile(const FileName: string): string;
var
  Processor: TUnifiedExcelProcessor;
  SheetNames: TStringList;
  I: Integer;
begin
  Result := '';
  Processor := TUnifiedExcelProcessor.Create;
  try
    if Processor.LoadFromFile(FileName) then
    begin
      SheetNames := Processor.GetSheetNames;
      try
        Result := '文件: ' + ExtractFileName(FileName) + #13#10;
        Result := Result + '工作表数量: ' + IntToStr(Processor.GetSheetCount) + #13#10;
        Result := Result + '工作表名称: ';
        for I := 0 to SheetNames.Count - 1 do
        begin
          if I > 0 then Result := Result + ', ';
          Result := Result + SheetNames[I];
        end;
      finally
        SheetNames.Free;
      end;
    end
    else
      Result := '无法读取文件: ' + ExtractFileName(FileName);
  finally
    Processor.Free;
  end;
end;

class function TExcelAPI.ReadSheetToDataSet(const FileName: string; SheetIndex: Integer): TFDMemTable;
var
  Processor: TUnifiedExcelProcessor;
begin
  Result := nil;
  Processor := TUnifiedExcelProcessor.Create;
  try
    if Processor.LoadFromFile(FileName) then
      Result := Processor.SheetToDataSet(SheetIndex);
  finally
    Processor.Free;
  end;
end;

class function TExcelAPI.ReadSheetToDataSet(const FileName: string; const SheetName: string): TFDMemTable;
var
  Processor: TUnifiedExcelProcessor;
  SheetNames: TStringList;
  SheetIndex: Integer;
begin
  Result := nil;
  Processor := TUnifiedExcelProcessor.Create;
  try
    if Processor.LoadFromFile(FileName) then
    begin
      SheetNames := Processor.GetSheetNames;
      try
        SheetIndex := SheetNames.IndexOf(SheetName);
        if SheetIndex >= 0 then
          Result := Processor.SheetToDataSet(SheetIndex);
      finally
        SheetNames.Free;
      end;
    end;
  finally
    Processor.Free;
  end;
end;

class function TExcelAPI.GetSheetNames(const FileName: string): TStringList;
var
  Processor: TUnifiedExcelProcessor;
begin
  Result := nil;
  Processor := TUnifiedExcelProcessor.Create;
  try
    if Processor.LoadFromFile(FileName) then
      Result := Processor.GetSheetNames;
  finally
    Processor.Free;
  end;
end;

class function TExcelAPI.IsValidExcelFile(const FileName: string): Boolean;
var
  Processor: TUnifiedExcelProcessor;
begin
  Processor := TUnifiedExcelProcessor.Create;
  try
    Result := Processor.IsValidExcelFile(FileName);
  finally
    Processor.Free;
  end;
end;

class function TExcelAPI.GetExcelFileInfo(const FileName: string): string;
var
  Processor: TUnifiedExcelProcessor;
  Sheet: TExcelSheet;
begin
  Result := '';
  Processor := TUnifiedExcelProcessor.Create;
  try
    if Processor.LoadFromFile(FileName) then
    begin
      Result := '文件信息:' + #13#10;
      Result := Result + '文件名: ' + ExtractFileName(FileName) + #13#10;
      Result := Result + '工作表数量: ' + IntToStr(Processor.GetSheetCount) + #13#10;

      if Processor.GetSheetCount > 0 then
      begin
        Sheet := Processor.GetSheet(0);
        if Assigned(Sheet) then
        begin
          Result := Result + '第一个工作表: ' + Sheet.Name + #13#10;
          Result := Result + '数据范围: ' + IntToStr(Sheet.RowCount) + '行 × ' + IntToStr(Sheet.ColCount) + '列' + #13#10;
          Result := Result + '包含数据: ' + BoolToStr(Sheet.HasData, True);
        end;
      end;
    end
    else
      Result := '无法读取文件: ' + ExtractFileName(FileName);
  finally
    Processor.Free;
  end;
end;

// 写入功能的静态方法
class function TExcelAPI.WriteDataSetToExcel(DataSet: TDataSet; const FileName: string; const SheetName: string): Boolean;
var
  Processor: TUnifiedExcelProcessor;
begin
  Result := False;
  Processor := TUnifiedExcelProcessor.Create;
  try
    if Processor.WriteDataSetToSheet(DataSet, SheetName) then
      Result := Processor.SaveToFile(FileName);
  finally
    Processor.Free;
  end;
end;



// 高级SST记录解析器 - 正确处理复杂的CONTINUE记录场景
function TUnifiedExcelProcessor.ParseSSTRecordAdvanced(const MainRecord: TBytes; const ContinueRecords: array of TBytes; StringTable: TStringList): Boolean;
type
  TRecordReader = record
    Data: TBytes;
    Position: Integer;
    IsContinue: Boolean;
    CompressFlag: Byte;
  end;

var
  TotalStrings, UniqueStrings: LongWord;
  I: Integer;
  Readers: array of TRecordReader;
  CurrentReader: Integer;
  StringLength: Word;
  OptionByte: Byte;
  IsCompressed: Boolean;
  StringValue: string;

  function ReaderAvailable(var Reader: TRecordReader): Integer;
  begin
    Result := Length(Reader.Data) - Reader.Position;
  end;

  function ReaderReadByte(var Reader: TRecordReader): Byte;
  begin
    if Reader.Position < Length(Reader.Data) then
    begin
      Result := Reader.Data[Reader.Position];
      Inc(Reader.Position);
    end
    else
      Result := 0;
  end;

  function ReaderReadWord(var Reader: TRecordReader): Word;
  begin
    if Reader.Position + 1 < Length(Reader.Data) then
    begin
      Result := Reader.Data[Reader.Position] or (Reader.Data[Reader.Position + 1] shl 8);
      Inc(Reader.Position, 2);
    end
    else
      Result := 0;
  end;

  // 跨记录读取字符串的核心函数
  function ReadStringAcrossRecords(StringLength: Word; InitialCompressed: Boolean): string;
  var
    CharsRead: Integer;
    IsCompressed: Boolean;
    Ch: Char;
  begin
    Result := '';
    CharsRead := 0;
    IsCompressed := InitialCompressed;
    SetLength(Result, StringLength);

    while (CharsRead < StringLength) and (CurrentReader < Length(Readers)) do
    begin
      // 检查当前记录是否有数据
      if ReaderAvailable(Readers[CurrentReader]) <= 0 then
      begin
        // 切换到下一个CONTINUE记录
        Inc(CurrentReader);
        if CurrentReader < Length(Readers) then
        begin
          // 读取新的压缩标志
          if Readers[CurrentReader].IsContinue then
          begin
            IsCompressed := (ReaderReadByte(Readers[CurrentReader]) and $01) = 0;
            ShowMessage('切换到CONTINUE记录#' + IntToStr(CurrentReader) + ', 新压缩标志: ' +
                       IntToStr(Ord(not IsCompressed)));
          end;
        end;
        Continue;
      end;

      // 读取字符
      if IsCompressed then
      begin
        // 8位字符
        Ch := Chr(ReaderReadByte(Readers[CurrentReader]));
      end
      else
      begin
        // 16位字符
        if ReaderAvailable(Readers[CurrentReader]) >= 2 then
          Ch := WideChar(ReaderReadWord(Readers[CurrentReader]))
        else
        begin
          ShowMessage('16位字符数据不足，切换记录');
          Inc(CurrentReader);
          Continue;
        end;
      end;

      Result[CharsRead + 1] := Ch;
      Inc(CharsRead);
    end;

    // 调整结果长度
    SetLength(Result, CharsRead);
  end;

begin
  Result := False;

  if Length(MainRecord) < 8 then
    Exit;

  // 设置记录读取器
  SetLength(Readers, 1 + Length(ContinueRecords));

  // 主记录
  Readers[0].Data := MainRecord;
  Readers[0].Position := 0;
  Readers[0].IsContinue := False;

  // CONTINUE记录
  for I := 0 to Length(ContinueRecords) - 1 do
  begin
    Readers[I + 1].Data := ContinueRecords[I];
    Readers[I + 1].Position := 0;
    Readers[I + 1].IsContinue := True;
  end;

  CurrentReader := 0;

  // 读取SST头部
  TotalStrings := ReaderReadWord(Readers[0]) or (ReaderReadWord(Readers[0]) shl 16);
  UniqueStrings := ReaderReadWord(Readers[0]) or (ReaderReadWord(Readers[0]) shl 16);

  ShowMessage('高级SST解析: TotalStrings=' + IntToStr(TotalStrings) + ', UniqueStrings=' + IntToStr(UniqueStrings));

  // 解析字符串
  for I := 0 to UniqueStrings - 1 do
  begin
    // 确保有足够数据读取字符串长度
    while (CurrentReader < Length(Readers)) and (ReaderAvailable(Readers[CurrentReader]) < 2) do
      Inc(CurrentReader);

    if CurrentReader >= Length(Readers) then
    begin
      ShowMessage('数据不足，无法继续解析');
      Break;
    end;

    StringLength := ReaderReadWord(Readers[CurrentReader]);
    OptionByte := ReaderReadByte(Readers[CurrentReader]);
    IsCompressed := (OptionByte and $01) = 0;

    // 特别调试关键字符串
    if (I >= 588) and (I <= 592) then
      ShowMessage('高级解析SST[' + IntToStr(I) + '] 长度=' + IntToStr(StringLength) +
                 ', 压缩=' + IntToStr(Ord(IsCompressed)) + ', 当前记录=' + IntToStr(CurrentReader));

    StringValue := ReadStringAcrossRecords(StringLength, IsCompressed);
    StringTable.Add(StringValue);

    if (I >= 588) and (I <= 592) then
      ShowMessage('高级解析SST[' + IntToStr(I) + '] 完成: "' + StringValue + '"');
  end;

  Result := True;
end;

end.
