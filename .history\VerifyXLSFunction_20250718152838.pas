program VerifyXLSFunction;

{$APPTYPE CONSOLE}

uses
  System.SysUtils,
  System.Classes,
  UnifiedExcelProcessor_POI;

procedure TestBIFFRecordWriter;
var
  Stream: TMemoryStream;
  Writer: TBIFFRecordWriter;
  StringTable: TStringList;
begin
  WriteLn('测试BIFF记录写入器...');
  
  Stream := TMemoryStream.Create;
  try
    Writer := TBIFFRecordWriter.Create(Stream, False);
    try
      // 写入BOF记录
      Writer.WriteBOFRecord($0005);
      WriteLn('BOF记录写入完成');
      
      // 创建字符串表
      StringTable := TStringList.Create;
      try
        StringTable.Add('Hello');
        StringTable.Add('World');
        StringTable.Add('Test');
        
        // 写入SST记录
        Writer.WriteSSTRecord(StringTable);
        WriteLn('SST记录写入完成');
        
        // 写入一些单元格
        Writer.WriteLabelSSTRecord(0, 0, 0); // "Hello"
        Writer.WriteLabelSSTRecord(0, 1, 1); // "World"
        Writer.WriteNumberRecord(1, 0, 123.45);
        Writer.WriteRKRecord(1, 1, 100);
        WriteLn('单元格记录写入完成');
        
        // 写入EOF记录
        Writer.WriteEOFRecord;
        WriteLn('EOF记录写入完成');
        
      finally
        StringTable.Free;
      end;
      
    finally
      Writer.Free;
    end;
    
    WriteLn('BIFF数据总大小: ' + IntToStr(Stream.Size) + ' 字节');
    
    // 保存到文件进行验证
    Stream.SaveToFile('test_biff.xls');
    WriteLn('BIFF数据已保存到 test_biff.xls');
    
  finally
    Stream.Free;
  end;
end;

procedure TestExcelProcessor;
var
  Processor: TUnifiedExcelProcessor;
  Sheet: TExcelSheet;
begin
  WriteLn('测试Excel处理器写入功能...');
  
  Processor := TUnifiedExcelProcessor.Create;
  try
    // 清空工作簿
    Processor.Workbook.ClearSheets;
    
    // 创建工作表
    Sheet := Processor.Workbook.AddSheet('TestSheet');
    if Assigned(Sheet) then
    begin
      // 添加一些测试数据
      Sheet.AddCell(0, 0, 'Name', cdtString);
      Sheet.AddCell(0, 1, 'Age', cdtString);
      Sheet.AddCell(0, 2, 'City', cdtString);
      
      Sheet.AddCell(1, 0, 'Alice', cdtString);
      Sheet.AddCell(1, 1, '25', cdtNumber);
      Sheet.AddCell(1, 2, 'Beijing', cdtString);
      
      Sheet.AddCell(2, 0, 'Bob', cdtString);
      Sheet.AddCell(2, 1, '30', cdtNumber);
      Sheet.AddCell(2, 2, 'Shanghai', cdtString);
      
      WriteLn('测试数据添加完成');
      
      // 保存到文件
      if Processor.SaveToFile('test_processor.xls') then
        WriteLn('Excel处理器保存成功: test_processor.xls')
      else
        WriteLn('Excel处理器保存失败');
    end
    else
      WriteLn('创建工作表失败');
      
  finally
    Processor.Free;
  end;
end;

procedure TestExcelAPI;
begin
  WriteLn('测试Excel API...');
  WriteLn('数组参数API测试已跳过（需要修复数组参数支持）');
end;

begin
  try
    WriteLn('=== XLS写入功能验证 ===');
    WriteLn('');
    
    TestBIFFRecordWriter;
    WriteLn('');
    
    TestExcelProcessor;
    WriteLn('');
    
    TestExcelAPI;
    WriteLn('');
    
    WriteLn('=== 验证完成 ===');
    WriteLn('检查生成的文件:');
    WriteLn('- test_biff.xls');
    WriteLn('- test_processor.xls');
    WriteLn('- test_api.xls');
    WriteLn('');
    WriteLn('按回车键退出...');
    ReadLn;
    
  except
    on E: Exception do
    begin
      WriteLn('验证过程中发生异常: ' + E.ClassName + ': ' + E.Message);
      WriteLn('按回车键退出...');
      ReadLn;
    end;
  end;
end.
