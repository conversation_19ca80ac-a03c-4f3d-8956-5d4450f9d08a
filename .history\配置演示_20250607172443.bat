@echo off
chcp 65001 >nul
echo.
echo ==========================================
echo    NewDBTool 配置功能演示
echo ==========================================
echo.

echo 1. 创建示例配置文件...
echo.

:: 创建深色主题配置
echo [Display] > dark_theme.ini
echo FontSize=10 >> dark_theme.ini
echo FontName=Microsoft YaHei >> dark_theme.ini
echo ShowGrid=True >> dark_theme.ini
echo ShowHeaders=True >> dark_theme.ini
echo. >> dark_theme.ini
echo [Colors] >> dark_theme.ini
echo SelectionColor=4210752 >> dark_theme.ini
echo SelectionTextColor=16777215 >> dark_theme.ini
echo HeaderColor=3158064 >> dark_theme.ini
echo ButtonColor=5263440 >> dark_theme.ini
echo. >> dark_theme.ini
echo [Behavior] >> dark_theme.ini
echo AutoSave=True >> dark_theme.ini
echo ConfirmDelete=True >> dark_theme.ini
echo EnableFormulas=True >> dark_theme.ini
echo DoubleClickEdit=True >> dark_theme.ini
echo. >> dark_theme.ini
echo [ColorButton] >> dark_theme.ini
echo ButtonWidth=25 >> dark_theme.ini
echo ButtonText=▼ >> dark_theme.ini
echo ShowButton=True >> dark_theme.ini

echo 深色主题配置已创建: dark_theme.ini

:: 创建简洁模式配置
echo [Display] > simple_theme.ini
echo FontSize=8 >> simple_theme.ini
echo FontName=Tahoma >> simple_theme.ini
echo ShowGrid=False >> simple_theme.ini
echo ShowHeaders=True >> simple_theme.ini
echo. >> simple_theme.ini
echo [Colors] >> simple_theme.ini
echo SelectionColor=16777215 >> simple_theme.ini
echo SelectionTextColor=0 >> simple_theme.ini
echo HeaderColor=15790320 >> simple_theme.ini
echo ButtonColor=15790320 >> simple_theme.ini
echo. >> simple_theme.ini
echo [Behavior] >> simple_theme.ini
echo AutoSave=False >> simple_theme.ini
echo ConfirmDelete=False >> simple_theme.ini
echo EnableFormulas=False >> simple_theme.ini
echo DoubleClickEdit=True >> simple_theme.ini
echo. >> simple_theme.ini
echo [ColorButton] >> simple_theme.ini
echo ButtonWidth=20 >> simple_theme.ini
echo ButtonText=... >> simple_theme.ini
echo ShowButton=False >> simple_theme.ini

echo 简洁模式配置已创建: simple_theme.ini

:: 创建传奇风格配置
echo [Display] > legend_theme.ini
echo FontSize=9 >> legend_theme.ini
echo FontName=SimSun >> legend_theme.ini
echo ShowGrid=True >> legend_theme.ini
echo ShowHeaders=True >> legend_theme.ini
echo. >> legend_theme.ini
echo [Colors] >> legend_theme.ini
echo SelectionColor=255 >> legend_theme.ini
echo SelectionTextColor=16777215 >> legend_theme.ini
echo HeaderColor=8388608 >> legend_theme.ini
echo ButtonColor=12632256 >> legend_theme.ini
echo. >> legend_theme.ini
echo [Behavior] >> legend_theme.ini
echo AutoSave=True >> legend_theme.ini
echo ConfirmDelete=True >> legend_theme.ini
echo EnableFormulas=True >> legend_theme.ini
echo DoubleClickEdit=True >> legend_theme.ini
echo. >> legend_theme.ini
echo [ColorButton] >> legend_theme.ini
echo ButtonWidth=30 >> legend_theme.ini
echo ButtonText=... >> legend_theme.ini
echo ShowButton=True >> legend_theme.ini

echo 传奇风格配置已创建: legend_theme.ini
echo.

echo 2. 配置文件使用说明:
echo.
echo    要应用这些配置，请按以下步骤操作：
echo.
echo    方法一 - 通过程序界面：
echo    1. 启动 NewDBTool.exe
echo    2. 选择菜单: 编辑 → 配置设置
echo    3. 在弹出的对话框中点击"是"
echo    4. 用记事本打开生成的配置文件
echo    5. 复制想要的主题配置内容到文件中
echo    6. 保存文件，然后在程序中点击"是"重新加载
echo.
echo    方法二 - 直接替换配置文件：
echo    1. 关闭 NewDBTool.exe（如果正在运行）
echo    2. 将想要的主题文件重命名为: NewDBTool_config.ini
echo    3. 启动 NewDBTool.exe
echo.

echo 3. 可用的主题配置：
echo.
echo    📱 dark_theme.ini     - 深色主题（适合长时间使用）
echo    🎯 simple_theme.ini   - 简洁模式（最小化界面元素）
echo    ⚔️  legend_theme.ini   - 传奇风格（经典游戏风格）
echo.

echo 4. 自定义配置提示：
echo.
echo    • 字体大小范围: 8-24
echo    • 推荐字体: Tahoma, Microsoft YaHei, SimSun
echo    • 颜色值使用RGB整数格式
echo    • True/False 值控制开关功能
echo    • 按钮宽度范围: 10-100 像素
echo.

echo 5. 常用颜色值参考：
echo.
echo    黑色: 0              白色: 16777215
echo    红色: 255            绿色: 65280
echo    蓝色: 16711680       浅蓝: 13166335
echo    深蓝: 4210752        浅灰: 15790320
echo    深灰: 3158064        金色: 255215
echo.

echo ==========================================
echo 演示完成！现在可以启动 NewDBTool 并测试配置功能。
echo ==========================================
echo.
pause
