﻿unit UnifiedExcelProcessor;

{
  统一Excel处理器 - 简化版本
  
  提供基本的Excel读取功能，暂时返回示例数据
  
  Author: AI Assistant
  Date: 2025-07-12
  Version: 1.0
}

interface

uses
  System.SysUtils, System.Classes, System.Variants, System.Math, System.StrUtils,
  Data.DB, FireDAC.Comp.DataSet, FireDAC.Comp.Client,
  Vcl.Dialogs, Winapi.Windows, System.Generics.Collections;

type
  // 单元格数据类型
  TCellDataType = (cdtEmpty, cdtString, cdtNumber, cdtDate, cdtBoolean, cdtError, cdtFormula);
  
  // 参考FastExcel的CellData结构
  TCellData = class
  private
    FDataType: TCellDataType;
  public
    Row: Integer;
    Col: Integer;

    // 不同类型的值存储（参考FastExcel）
    StringValue: string;      // 字符串值
    NumberValue: Double;      // 数字值
    BooleanValue: Boolean;    // 布尔值
    OriginalValue: string;    // 原始值（用于调试）

    constructor Create(ARow, ACol: Integer; const AValue: string; ADataType: TCellDataType = cdtString);

    // 类型安全的值获取
    function GetDisplayValue: string;
    function IsEmpty: Boolean;

    // 属性
    property DataType: TCellDataType read FDataType write FDataType;
    property Value: string read GetDisplayValue;
  end;
  
  // 工作表数据
  TExcelSheet = class
  private
    FName: string;
    FCells: TList;
    FCellMap: TDictionary<string, TCellData>; // 哈希表优化查找
    FRowCount: Integer;
    FColCount: Integer;
    
    procedure UpdateBounds(Row, Col: Integer);
    function FindCell(Row, Col: Integer): TCellData;
    
  public
    constructor Create(const AName: string = 'Sheet1');
    destructor Destroy; override;
    
    procedure AddCell(Row, Col: Integer; const Value: string; DataType: TCellDataType = cdtString);
    function GetCellValue(Row, Col: Integer): string;
    function HasData: Boolean;
    function ToDataSet: TFDMemTable;
    
    property Name: string read FName write FName;
    property RowCount: Integer read FRowCount;
    property ColCount: Integer read FColCount;
  end;
  
  // Excel工作簿
  TExcelWorkbook = class
  private
    FSheets: TList;
    FFileName: string;
    
  public
    constructor Create;
    destructor Destroy; override;
    
    function AddSheet(const Name: string = ''): TExcelSheet;
    function GetSheet(Index: Integer): TExcelSheet; overload;
    function GetSheetCount: Integer;
    function GetSheetNames: TStringList;
    procedure ClearSheets;
    
    property FileName: string read FFileName write FFileName;
  end;
  
  // 统一Excel处理器主类（简化版）
  TUnifiedExcelProcessor = class
  private
    FWorkbook: TExcelWorkbook;

    function TryParseBiffData(Stream: TStream): Boolean;
    function FindBiffStart(Stream: TStream): Boolean;
    function FindNextValidRecord(Stream: TStream; StartPos, MaxPos: Int64): Boolean;
    function ParseSSTRecord(Stream: TStream; RecordLength: Word; StringTable: TStringList): Boolean;
    function DecodeRKValue(B1, B2, B3, B4: Byte): string;
    
  public
    constructor Create;
    destructor Destroy; override;
    
    // 读取功能
    function LoadFromFile(const FileName: string): Boolean;
    function LoadFromStream(Stream: TStream): Boolean;
    
    // 访问功能
    function GetSheetCount: Integer;
    function GetSheet(Index: Integer): TExcelSheet;
    function GetSheetNames: TStringList;
    
    // 转换功能
    function SheetToDataSet(Index: Integer): TFDMemTable;
    
    // 实用功能
    function IsValidExcelFile(const FileName: string): Boolean;
    
    property Workbook: TExcelWorkbook read FWorkbook;
  end;
  
  // 静态API接口类
  TExcelAPI = class
  public
    class function ReadExcelFile(const FileName: string): TUnifiedExcelProcessor;
    class function ReadSheetToDataSet(const FileName: string; SheetIndex: Integer = 0): TFDMemTable; overload;
    class function ReadSheetToDataSet(const FileName: string; const SheetName: string): TFDMemTable; overload;
    class function GetSheetNames(const FileName: string): TStringList;
    class function IsValidExcelFile(const FileName: string): Boolean;
    class function GetExcelFileInfo(const FileName: string): string;
  end;

implementation

{ TCellData }

constructor TCellData.Create(ARow, ACol: Integer; const AValue: string; ADataType: TCellDataType);
begin
  inherited Create;
  Row := ARow;
  Col := ACol;
  FDataType := ADataType;
  OriginalValue := AValue;

  // 根据数据类型设置相应的值
  case ADataType of
    cdtString: StringValue := AValue;
    cdtNumber:
    begin
      try
        NumberValue := StrToFloat(AValue);
      except
        NumberValue := 0;
        StringValue := AValue; // 如果转换失败，保存为字符串
      end;
    end;
    cdtBoolean: BooleanValue := (AValue = 'TRUE') or (AValue = '1');
    else
      StringValue := AValue;
  end;
end;

function TCellData.GetDisplayValue: string;
begin
  case DataType of
    cdtString: Result := StringValue;
    cdtNumber: Result := FloatToStr(NumberValue);
    cdtBoolean: Result := BoolToStr(BooleanValue, True);
    cdtEmpty: Result := '';
    else
      Result := OriginalValue;
  end;
end;

function TCellData.IsEmpty: Boolean;
begin
  case DataType of
    cdtEmpty: Result := True;
    cdtString: Result := Trim(StringValue) = '';
    cdtNumber: Result := NumberValue = 0;
    cdtBoolean: Result := False;
    else
      Result := OriginalValue = '';
  end;
end;

{ TExcelSheet }

constructor TExcelSheet.Create(const AName: string);
begin
  inherited Create;
  FName := AName;
  if FName = '' then
    FName := 'Sheet1';
  FCells := TList.Create;
  FCellMap := TDictionary<string, TCellData>.Create;
  FRowCount := 0;
  FColCount := 0;
end;

destructor TExcelSheet.Destroy;
var
  I: Integer;
begin
  for I := 0 to FCells.Count - 1 do
    TCellData(FCells[I]).Free;
  FCells.Free;
  FCellMap.Free;
  inherited;
end;

procedure TExcelSheet.UpdateBounds(Row, Col: Integer);
begin
  if Row >= FRowCount then
    FRowCount := Row + 1;
  if Col >= FColCount then
    FColCount := Col + 1;
end;

function TExcelSheet.FindCell(Row, Col: Integer): TCellData;
var
  Key: string;
begin
  Key := IntToStr(Row) + ',' + IntToStr(Col);
  if not FCellMap.TryGetValue(Key, Result) then
    Result := nil;
end;

procedure TExcelSheet.AddCell(Row, Col: Integer; const Value: string; DataType: TCellDataType);
var
  Cell: TCellData;
  Key: string;
begin
  Cell := TCellData.Create(Row, Col, Value, DataType);
  FCells.Add(Cell);

  // 同时添加到哈希表，实现O(1)查找
  Key := IntToStr(Row) + ',' + IntToStr(Col);
  FCellMap.AddOrSetValue(Key, Cell);

  UpdateBounds(Row, Col);

  // 关键调试：每1000个单元格显示一次进度（减少弹窗）
  // 单元格添加进度跟踪（已删除调试信息）
end;

function TExcelSheet.GetCellValue(Row, Col: Integer): string;
var
  Cell: TCellData;
begin
  Cell := FindCell(Row, Col);
  if Assigned(Cell) then
    Result := Cell.Value
  else
    Result := '';
end;

function TExcelSheet.HasData: Boolean;
begin
  Result := FCells.Count > 0;
end;

function TExcelSheet.ToDataSet: TFDMemTable;
var
  I, J, TestRow: Integer;
  FieldName: string;
  Cell: TCellData;
  FieldIndex: Integer;
  HasData: Boolean;
begin
  Result := TFDMemTable.Create(nil);
  try
    HasData := Self.HasData; // 初始化HasData变量
    if not HasData then
      Exit;
    
    // 如果没有数据，就不创建任何字段
    if (FRowCount = 0) or (FColCount = 0) then
      Exit;

    // 参考FastExcel思路：为所有包含数据的列创建字段
    // 第一步：检查哪些列包含数据（检查所有行，不仅仅是第一行）
    for J := 0 to FColCount - 1 do
    begin
      HasData := False;
      // 检查这一列是否在任何行中有数据
      for I := 0 to FRowCount - 1 do
      begin
        Cell := FindCell(I, J);
        if Assigned(Cell) and (Trim(Cell.Value) <> '') then
        begin
          HasData := True;
          Break;
        end;
      end;

      // 如果这一列有数据，就创建字段
      if HasData then
      begin
        // 尝试使用第一行作为字段名
        Cell := FindCell(0, J);
        if Assigned(Cell) and (Trim(Cell.Value) <> '') then
        begin
          FieldName := Trim(Cell.Value);
          // 只对字段名为"attr"的字段设置4000字符长度，其他字段保持255字符
          if FieldName = 'attr' then
            Result.FieldDefs.Add(FieldName, ftWideString, 4000)
          else
            Result.FieldDefs.Add(FieldName, ftWideString, 255);
        end
        else
        begin
          // 如果第一行没有字段名，就不创建字段定义
          // 绝对不创建虚假的列名
        end;
      end;
    end;

    // 字段创建完成

    Result.CreateDataSet;
    Result.Active := True;

    // 第二步：添加数据（从第二行开始，第一行作为字段名）
    for I := 1 to FRowCount - 1 do
    begin
      Result.Append;
      FieldIndex := 0;
      for J := 0 to FColCount - 1 do
      begin
        // 检查这一列是否有字段（即是否包含数据）
        HasData := False;
        for TestRow := 0 to FRowCount - 1 do
        begin
          Cell := FindCell(TestRow, J);
          if Assigned(Cell) and (Trim(Cell.Value) <> '') then
          begin
            HasData := True;
            Break;
          end;
        end;

        // 如果这一列有字段，就填充数据
        if HasData then
        begin
          Cell := FindCell(I, J);
          if Assigned(Cell) and (Trim(Cell.Value) <> '') then
            Result.Fields[FieldIndex].AsString := Trim(Cell.Value)
          else
            Result.Fields[FieldIndex].AsString := ''; // 空值
          Inc(FieldIndex);
        end;
      end;
      Result.Post;
    end;
    
    Result.First;
  except
    Result.Free;
    raise;
  end;
end;

{ TExcelWorkbook }

constructor TExcelWorkbook.Create;
begin
  inherited Create;
  FSheets := TList.Create;
end;

destructor TExcelWorkbook.Destroy;
var
  I: Integer;
begin
  for I := 0 to FSheets.Count - 1 do
    TExcelSheet(FSheets[I]).Free;
  FSheets.Free;
  inherited;
end;

function TExcelWorkbook.AddSheet(const Name: string): TExcelSheet;
var
  SheetName: string;
begin
  SheetName := Name;
  if SheetName = '' then
    SheetName := 'Sheet' + IntToStr(FSheets.Count + 1);
  
  Result := TExcelSheet.Create(SheetName);
  FSheets.Add(Result);
end;

function TExcelWorkbook.GetSheet(Index: Integer): TExcelSheet;
begin
  if (Index >= 0) and (Index < FSheets.Count) then
    Result := TExcelSheet(FSheets[Index])
  else
    Result := nil;
end;

function TExcelWorkbook.GetSheetCount: Integer;
begin
  Result := FSheets.Count;
end;

function TExcelWorkbook.GetSheetNames: TStringList;
var
  I: Integer;
begin
  Result := TStringList.Create;
  for I := 0 to FSheets.Count - 1 do
    Result.Add(TExcelSheet(FSheets[I]).Name);
end;

procedure TExcelWorkbook.ClearSheets;
var
  I: Integer;
begin
  for I := 0 to FSheets.Count - 1 do
    TExcelSheet(FSheets[I]).Free;
  FSheets.Clear;
end;

{ TUnifiedExcelProcessor }

constructor TUnifiedExcelProcessor.Create;
begin
  inherited Create;
  FWorkbook := TExcelWorkbook.Create;
end;

destructor TUnifiedExcelProcessor.Destroy;
begin
  FWorkbook.Free;
  inherited;
end;



// 寻找下一个有效的BIFF记录
function TUnifiedExcelProcessor.FindNextValidRecord(Stream: TStream; StartPos, MaxPos: Int64): Boolean;
var
  TestBuffer: array[0..3] of Byte;
  TestPos: Int64;
  TestType, TestLength: Word;
begin
  Result := False;
  TestPos := StartPos + 1;

  while TestPos < MaxPos - 4 do
  begin
    Stream.Position := TestPos;
    if Stream.Read(TestBuffer, 4) = 4 then
    begin
      TestType := TestBuffer[0] or (TestBuffer[1] shl 8);
      TestLength := TestBuffer[2] or (TestBuffer[3] shl 8);

      // 检查是否为已知的BIFF记录类型且长度合理
      if (TestLength > 0) and (TestLength <= 8224) and
         ((TestType = $0809) or (TestType = $000A) or (TestType = $00FC) or
          (TestType = $00FD) or (TestType = $0204) or (TestType = $0203) or
          (TestType = $027E) or (TestType = $0201) or (TestType = $00BD)) then
      begin
        Stream.Position := TestPos;
        Result := True;
        Exit;
      end;
    end;
    Inc(TestPos);
  end;
end;

function TUnifiedExcelProcessor.TryParseBiffData(Stream: TStream): Boolean;
var
  Buffer: array[0..8191] of Byte; // 恢复到8KB缓冲区，确保稳定性
  RecordType, RecordLength: Word;
  Sheet: TExcelSheet;
  BytesRead: Integer;
  CurrentPos: Int64;
  MaxPos: Int64;
  SheetCount: Integer;
  CellRow, CellCol: Word;
  CellValue: string;
  StringLength: Word;
  DoubleValue: Double;
  SSTIndex: LongWord;
  StringTable: TStringList;
  BOFType: Word;
  TestCol, TestRow: Integer;
  TestFieldName, TestValue: string;
  RecordCount, CellRecordCount: Integer;
  CellsFound, BytesToRead, I: Integer;
  NextPos: Int64;
  FirstRow, LastRow, FirstCol, LastCol: Word;
  RKValue: Cardinal;
begin
  Result := False;
  SheetCount := 0;
  StringTable := TStringList.Create;

  // 开始解析BIFF数据

  try
    Stream.Position := 0;
    MaxPos := Stream.Size;

    // 寻找BIFF数据的开始位置
    if not FindBiffStart(Stream) then
      Exit;

    // 创建默认工作表
    Sheet := FWorkbook.AddSheet('Sheet1');
    if not Assigned(Sheet) then
      Exit;

    // 开始解析BIFF记录

    // 改进的BIFF记录解析，添加调试计数
    RecordCount := 0;
    CellRecordCount := 0;

    // 基于POI的流式读取方法：逐记录读取，避免大缓冲区问题
    while Stream.Position < MaxPos - 4 do
    begin
      CurrentPos := Stream.Position;
      Inc(RecordCount);

      // 读取记录头（4字节：类型2字节 + 长度2字节）
      BytesRead := Stream.Read(Buffer, 4);
      if BytesRead < 4 then
        Break;

      RecordType := Buffer[0] or (Buffer[1] shl 8);
      RecordLength := Buffer[2] or (Buffer[3] shl 8);

      // 强化的数据验证：检查是否为有效的BIFF记录
      // 检查异常大的长度值（可能是损坏的数据）
      if (RecordLength > 8224) or (RecordLength = 65535) then
      begin
        ShowMessage('记录#' + IntToStr(RecordCount) + ' 长度异常: ' + IntToStr(RecordLength) +
                   ' (原始字节: ' + IntToHex(Buffer[2], 2) + ' ' + IntToHex(Buffer[3], 2) + ')');

        // 尝试寻找下一个有效的BOF记录来恢复
        if not FindNextValidRecord(Stream, CurrentPos, MaxPos) then
        begin
          ShowMessage('无法找到有效记录，停止解析');
          Break;
        end;
        Continue;
      end;

      if (CurrentPos + RecordLength + 4 > MaxPos) then
      begin
        ShowMessage('记录#' + IntToStr(RecordCount) + ' 超出文件范围: 位置=' + IntToStr(CurrentPos) + ', 长度=' + IntToStr(RecordLength));
        Break; // 超出文件范围，停止解析
      end;


      // 记录所有遇到的记录类型（用于分析）
      //if (RecordCount <= 50) or (RecordType = $00FD) or (RecordType = $0204) or
       //  (RecordType = $0203) or (RecordType = $027E) or (RecordType = $0201) or
       //  (RecordType = $0200) then
        //ShowMessage('记录#' + IntToStr(RecordCount) + ': 类型$' + IntToHex(RecordType, 4) + ', 长度' + IntToStr(RecordLength));


      // 基于POI的完整BIFF记录类型支持
      case RecordType of
        // === 文件结构记录 ===
        $0809: // BOF (Beginning of File)
        begin
          if RecordLength >= 4 then
          begin
            Stream.Read(Buffer, 4);
            BOFType := Buffer[2] or (Buffer[3] shl 8);
            if BOFType = $0010 then // 工作表BOF
              Inc(SheetCount);
            Stream.Position := Stream.Position - 4;
          end;
        end;

        $000A: // EOF (End of File)
        begin
          // EOF记录处理
        end;

        // === 字符串表记录 ===
        $00FC: // SST (Shared String Table)
        begin
          ParseSSTRecord(Stream, RecordLength, StringTable);
        end;

        $00FF: // EXT_SST (Extended SST)
        begin
          // 跳过扩展SST记录
        end;

        // === 单元格数据记录（基于POI完整支持）===
        $00FD: // LABEL_SST - 使用共享字符串的文本单元格
        begin
          if RecordLength >= 10 then
          begin
            // POI式安全检查：确保有足够的数据可读
            if (Stream.Position + 10 <= MaxPos) then
            begin
              // 只读取需要的10字节，避免大缓冲区
              if Stream.Read(Buffer, 10) = 10 then
            begin
              CellRow := Buffer[0] or (Buffer[1] shl 8);
              CellCol := Buffer[2] or (Buffer[3] shl 8);
              SSTIndex := Buffer[6] or (Buffer[7] shl 8) or (Buffer[8] shl 16) or (Buffer[9] shl 24);

              if (Integer(SSTIndex) < StringTable.Count) then
              begin
                CellValue := StringTable[SSTIndex];
                Sheet.AddCell(CellRow, CellCol, CellValue, cdtString);
                Inc(CellRecordCount);
                if CellRecordCount <= 5 then
                  ShowMessage('LabelSST数据: [' + IntToStr(CellRow) + ',' + IntToStr(CellCol) + '] = ' + CellValue);
              end;

                // 跳过剩余的记录数据
                if RecordLength > 10 then
                  Stream.Position := Stream.Position + (RecordLength - 10);
              end;
            end
            else
            begin
              ShowMessage('LABEL_SST记录数据不足，跳过');
            end;
          end
          else
          begin
            // 跳过整个记录
            Stream.Position := Stream.Position + RecordLength;
          end;
        end;

        $0204: // LABEL - 直接文本单元格（按POI格式）
        begin
          if RecordLength >= 9 then // 最少需要9字节：行(2)+列(2)+XF(2)+长度(2)+Unicode标志(1)
          begin
            Stream.Read(Buffer, 9);
            CellRow := Buffer[0] or (Buffer[1] shl 8);
            CellCol := Buffer[2] or (Buffer[3] shl 8);
            // 跳过XF索引 Buffer[4-5]
            StringLength := Buffer[6] or (Buffer[7] shl 8);
            // Unicode标志在Buffer[8]

            if (StringLength > 0) and (StringLength <= RecordLength - 9) then
            begin
              if (Buffer[8] and $01) <> 0 then
              begin
                // Unicode字符串 (16位) - 使用动态缓冲区避免长度限制
                if StringLength * 2 <= RecordLength - 9 then
                begin
                  // 为长字符串分配专用缓冲区
                  var UnicodeBuffer: TBytes;
                  SetLength(UnicodeBuffer, StringLength * 2);
                  if Stream.Read(UnicodeBuffer[0], StringLength * 2) = StringLength * 2 then
                  begin
                    SetLength(CellValue, StringLength);
                    for I := 0 to StringLength - 1 do
                      CellValue[I + 1] := Char(UnicodeBuffer[I * 2] or (UnicodeBuffer[I * 2 + 1] shl 8));
                  end;
                end;
              end
              else
              begin
                // 压缩字符串 (8位) - 使用动态缓冲区避免长度限制
                var AnsiBuffer: TBytes;
                SetLength(AnsiBuffer, StringLength);
                if Stream.Read(AnsiBuffer[0], StringLength) = StringLength then
                begin
                  SetString(CellValue, PAnsiChar(@AnsiBuffer[0]), StringLength);
                  CellValue := string(CellValue);
                end;
              end;

              Sheet.AddCell(CellRow, CellCol, CellValue, cdtString);
              Inc(CellRecordCount);
              if CellRecordCount <= 5 then
                ShowMessage('Label数据: [' + IntToStr(CellRow) + ',' + IntToStr(CellCol) + '] = ' + CellValue);
            end;
          end;
        end;


        $0203: // NUMBER - 数字单元格
        begin
          if RecordLength >= 14 then
          begin
            if Stream.Read(Buffer, 14) = 14 then
            begin
              CellRow := Buffer[0] or (Buffer[1] shl 8);
              CellCol := Buffer[2] or (Buffer[3] shl 8);
              // 跳过XF索引Buffer[4-5]，读取8字节的IEEE 754双精度浮点数
              Move(Buffer[6], DoubleValue, 8);
              CellValue := FloatToStr(DoubleValue);
              Sheet.AddCell(CellRow, CellCol, CellValue, cdtNumber);
              Inc(CellRecordCount);
              if CellRecordCount <= 5 then
                ShowMessage('Number数据: [' + IntToStr(CellRow) + ',' + IntToStr(CellCol) + '] = ' + CellValue);

              // 跳过剩余数据
              if RecordLength > 14 then
                Stream.Position := Stream.Position + (RecordLength - 14);
            end;
          end
          else
          begin
            Stream.Position := Stream.Position + RecordLength;
          end;
        end;

        $027E: // RK (压缩数字)
        begin
          if RecordLength >= 10 then
          begin
            if Stream.Read(Buffer, 10) = 10 then
            begin
              CellRow := Buffer[0] or (Buffer[1] shl 8);
              CellCol := Buffer[2] or (Buffer[3] shl 8);
              // 使用改进的RK解码（跳过XF索引Buffer[4-5]）
              CellValue := DecodeRKValue(Buffer[6], Buffer[7], Buffer[8], Buffer[9]);
              Sheet.AddCell(CellRow, CellCol, CellValue, cdtNumber);
              Inc(CellRecordCount);
              if CellRecordCount <= 5 then
                ShowMessage('RK数据: [' + IntToStr(CellRow) + ',' + IntToStr(CellCol) + '] = ' + CellValue);

              // 跳过剩余数据
              if RecordLength > 10 then
                Stream.Position := Stream.Position + (RecordLength - 10);
            end;
          end
          else
          begin
            Stream.Position := Stream.Position + RecordLength;
          end;
        end;



        $0201: // BLANK (空单元格)
        begin
          if RecordLength >= 6 then
          begin
            Stream.Read(Buffer, 6);
            CellRow := Buffer[0] or (Buffer[1] shl 8);
            CellCol := Buffer[2] or (Buffer[3] shl 8);
            Sheet.AddCell(CellRow, CellCol, '', cdtString);
            Inc(CellRecordCount);
          end;
        end;

        $0006: // FORMULA - 公式记录（基于POI实现）
        begin
          if RecordLength >= 20 then
          begin
            Stream.Read(Buffer, 20);
            CellRow := Buffer[0] or (Buffer[1] shl 8);
            CellCol := Buffer[2] or (Buffer[3] shl 8);

            // 读取公式的计算值（8字节double）
            Move(Buffer[6], DoubleValue, 8);

            // 检查是否为特殊值（字符串、布尔、错误）
            if (Buffer[12] = $FF) and (Buffer[13] = $FF) then
            begin
              // 特殊值，需要读取后续的STRING记录
              CellValue := '#FORMULA#'; // 占位符，等待STRING记录
            end
            else
            begin
              // 数字结果
              CellValue := FloatToStr(DoubleValue);
            end;

            Sheet.AddCell(CellRow, CellCol, CellValue, cdtFormula);
            Inc(CellRecordCount);
            if CellRecordCount <= 5 then
              ShowMessage('Formula数据: [' + IntToStr(CellRow) + ',' + IntToStr(CellCol) + '] = ' + CellValue);

            // 跳过公式表达式部分
            if RecordLength > 20 then
              Stream.Position := Stream.Position + (RecordLength - 20);
          end;
        end;

        $0085: // BOUND_SHEET - 工作表边界记录
        begin
          if RecordLength >= 8 then
          begin
            Stream.Read(Buffer, RecordLength);
            // 可以从这里提取工作表名称，但暂时跳过
          end;
        end;

        $0200: // DIMENSIONS - 工作表维度记录
        begin
          if RecordLength >= 10 then
          begin
            Stream.Read(Buffer, 10);
            FirstRow := Buffer[0] or (Buffer[1] shl 8);
            LastRow := Buffer[2] or (Buffer[3] shl 8);
            FirstCol := Buffer[4] or (Buffer[5] shl 8);
            LastCol := Buffer[6] or (Buffer[7] shl 8);
            // 更新工作表维度信息（如果需要的话）
          end;
        end;

        $0208: // ROW - 行记录
        begin
          // 跳过行记录
        end;

        $020B: // INDEX - 索引记录
        begin
          // 跳过索引记录
        end;

        $00BE: // MUL_BLANK - 多个空白单元格记录
        begin
          if RecordLength >= 6 then
          begin
            Stream.Read(Buffer, RecordLength);
            CellRow := Buffer[0] or (Buffer[1] shl 8);
            FirstCol := Buffer[2] or (Buffer[3] shl 8);
            LastCol := Buffer[RecordLength-2] or (Buffer[RecordLength-1] shl 8);

            // 为每个空白单元格创建记录
            for I := FirstCol to LastCol do
            begin
              Sheet.AddCell(CellRow, I, '', cdtEmpty);
              Inc(CellRecordCount);
            end;
          end;
        end;

        $003C: // CONTINUE - 继续记录
        begin
          // 跳过继续记录
        end;

        $0031: // FONT - 字体记录
        begin
          // 跳过字体记录
        end;

        $041E: // FORMAT - 格式记录
        begin
          // 跳过格式记录
        end;

        $00E0: // EXTENDED_FORMAT - 扩展格式记录
        begin
          // 跳过扩展格式记录
        end;

        // 添加更多可能包含数据的记录类型
        $0042: // CODEPAGE - 代码页记录
        begin
          // 跳过代码页记录
        end;

        $0161: // DSF - 双流文件记录
        begin
          // 跳过DSF记录
        end;

        $005C: // WRITE_ACCESS - 写访问记录
        begin
          // 跳过写访问记录
        end;

        $0013: // PASSWORD - 密码记录
        begin
          // 跳过密码记录
        end;

        $0040: // BACKUP - 备份记录
        begin
          // 跳过备份记录
        end;

        $003D: // WINDOW_ONE - 窗口记录
        begin
          // 跳过窗口记录
        end;

        $0092: // PALETTE - 调色板记录
        begin
          // 跳过调色板记录
        end;

        $013D: // TAB_ID - 标签ID记录
        begin
          // 跳过标签ID记录
        end;

        // 重要：添加可能被遗漏的单元格数据记录类型
        $0207: // STRING - 字符串记录（公式结果）
        begin
          if RecordLength >= 3 then
          begin
            // 先读取字符串长度
            if Stream.Read(Buffer, 2) = 2 then
            begin
              StringLength := Buffer[0] or (Buffer[1] shl 8);
              if (StringLength > 0) and (StringLength <= RecordLength - 2) then
              begin
                // 使用动态缓冲区读取字符串数据
                var StringBuffer: TBytes;
                SetLength(StringBuffer, StringLength);
                if Stream.Read(StringBuffer[0], StringLength) = StringLength then
                begin
                  SetString(CellValue, PAnsiChar(@StringBuffer[0]), StringLength);
                  CellValue := string(CellValue);
                  Inc(CellRecordCount);
                  if CellRecordCount <= 5 then
                    ShowMessage('String数据: ' + CellValue);
                end;

                // 跳过剩余数据
                if RecordLength > StringLength + 2 then
                  Stream.Position := Stream.Position + (RecordLength - StringLength - 2);
              end
              else
              begin
                // 跳过整个记录
                Stream.Position := Stream.Position + (RecordLength - 2);
              end;
            end;
          end
          else
          begin
            Stream.Position := Stream.Position + RecordLength;
          end;
        end;

        $0221: // ARRAY - 数组公式记录
        begin
          if RecordLength >= 14 then
          begin
            Stream.Read(Buffer, 14);
            FirstRow := Buffer[0] or (Buffer[1] shl 8);
            LastRow := Buffer[2] or (Buffer[3] shl 8);
            FirstCol := Buffer[4];
            LastCol := Buffer[5];
            // 跳过数组公式的详细处理，只记录范围
            for CellRow := FirstRow to LastRow do
              for CellCol := FirstCol to LastCol do
              begin
                Sheet.AddCell(CellRow, CellCol, '#ARRAY#', cdtFormula);
                Inc(CellRecordCount);
              end;
            // 跳过剩余的公式数据
            if RecordLength > 14 then
              Stream.Position := Stream.Position + (RecordLength - 14);
          end;
        end;

        $04BC: // SHARED_FORMULA - 共享公式记录
        begin
          // 跳过共享公式记录，这通常与其他单元格记录配合使用
        end;

        $0205: // BOOL_ERR - 布尔/错误记录
        begin
          if RecordLength >= 8 then
          begin
            Stream.Read(Buffer, 8);
            CellRow := Buffer[0] or (Buffer[1] shl 8);
            CellCol := Buffer[2] or (Buffer[3] shl 8);
            // Buffer[6] 包含布尔值或错误代码
            // Buffer[7] 指示是布尔值(0)还是错误值(1)
            if Buffer[7] = 0 then
              CellValue := IfThen(Buffer[6] = 0, 'FALSE', 'TRUE')
            else
              CellValue := '#ERROR';
            Sheet.AddCell(CellRow, CellCol, CellValue, cdtString);
            Inc(CellRecordCount);
          end;
        end;

        $00BD: // MulRK - 参考POI实现的正确解析
        begin
          if (RecordLength >= 8) and (RecordLength <= 8192) then
          begin
            Stream.Read(Buffer, RecordLength);
            CellRow := Buffer[0] or (Buffer[1] shl 8);
            FirstCol := Buffer[2] or (Buffer[3] shl 8);
            LastCol := Buffer[RecordLength-2] or (Buffer[RecordLength-1] shl 8);

            // MulRK记录解析

            // 参考POI实现：格式为 行号(2) + 第一列(2) + [XF索引(2) + RK值(4)]... + 最后列(2)
            // 每个RK记录占6字节：XF索引(2字节) + RK值(4字节)
            I := 4; // 跳过行号和第一列号
            CellCol := FirstCol; // 从第一列开始

            while I <= RecordLength - 8 do // 保留最后2字节给LastCol
            begin
              // 跳过XF索引(2字节)，直接读取RK值(4字节)
              RKValue := Buffer[I+2] or (Buffer[I+3] shl 8) or (Buffer[I+4] shl 16) or (Buffer[I+5] shl 24);
              CellValue := DecodeRKValue(Buffer[I+2], Buffer[I+3], Buffer[I+4], Buffer[I+5]);
              Sheet.AddCell(CellRow, CellCol, CellValue, cdtNumber);
              Inc(CellRecordCount);

              Inc(CellCol); // 下一列（连续的）
              Inc(I, 6); // 下一个RK记录：2字节XF索引 + 4字节RK值
            end;
          end
          else
          begin
            // MulRK记录过大，跳过
          end;
        end;
      end;

      // 统一移动到下一个记录（基于POI的安全检查）
      NextPos := CurrentPos + RecordLength + 4;

      // POI式的边界检查：确保不会超出文件范围
      if NextPos > MaxPos then
      begin
        // 位置超出文件范围，停止解析
        ShowMessage('警告: 记录#' + IntToStr(RecordCount) + ' 位置超出文件范围，停止解析');
        Break;
      end;

      // 安全设置流位置
      try
        Stream.Position := NextPos;
      except
        on E: Exception do
        begin
          // 流位置设置失败，尝试恢复
          ShowMessage('流位置异常: ' + E.Message + ', 尝试恢复');
          Stream.Position := CurrentPos + 1;
          Continue;
        end;
      end;

      // 验证流位置是否正确（每100个记录检查一次）
     // if (RecordCount mod 100 = 0) then
       // ShowMessage('记录#' + IntToStr(RecordCount) + ' 位置验证: 当前=' + IntToStr(Stream.Position) + ', 预期=' + IntToStr(NextPos));
    end;

    // 解析完成，更新工作表统计信息
    ShowMessage('解析完成 - 总记录数: ' + IntToStr(RecordCount) + ', 单元格记录数: ' + IntToStr(CellRecordCount));
    ShowMessage('Sheet.FCells.Count: ' + IntToStr(Sheet.FCells.Count) + ', HasData: ' + BoolToStr(Sheet.HasData, True));

    // 显示数据分布信息
    ShowMessage('数据范围: 行数=' + IntToStr(Sheet.FRowCount) + ', 列数=' + IntToStr(Sheet.FColCount));

    // 显示数据分布统计
    // ShowDataDistribution(Sheet); // 暂时注释掉

    // 改进的结果判断：即使没有找到单元格数据，如果找到了足够的BIFF记录，也认为是有效的Excel文件
    Result := Sheet.HasData or (RecordCount > 10) or (StringTable.Count > 0);

    if not Result then
      ShowMessage('未找到有效的Excel数据 - 单元格数: ' + IntToStr(Sheet.FCells.Count) + ', 记录数: ' + IntToStr(RecordCount) + ', 字符串表: ' + IntToStr(StringTable.Count))
    else
      ShowMessage('Excel解析成功 - 数据源: ' +
        IfThen(Sheet.HasData, '单元格数据', '') +
        IfThen(RecordCount > 10, ' BIFF记录', '') +
        IfThen(StringTable.Count > 0, ' 字符串表', ''));

  except
    on E: Exception do
    begin
      // BIFF解析异常: E.Message
      Result := False;
    end;
  end;

  StringTable.Free;
end;

function TUnifiedExcelProcessor.FindBiffStart(Stream: TStream): Boolean;
var
  Buffer: array[0..7] of Byte;
  I: Integer;
  MaxSearch: Int64;
  CommonPositions: array[0..3] of Integer;
  FileHeader: string;
begin
  Result := False;
  Stream.Position := 0;
  MaxSearch := Min(Stream.Size, 8192); // 扩大搜索范围到8KB

  // 显示文件头信息用于调试
  if Stream.Read(Buffer, 8) = 8 then
  begin
    FileHeader := Format('文件头: %02X %02X %02X %02X %02X %02X %02X %02X',
      [Buffer[0], Buffer[1], Buffer[2], Buffer[3], Buffer[4], Buffer[5], Buffer[6], Buffer[7]]);
    ShowMessage(FileHeader);
  end;

  Stream.Position := 0;

  // 寻找BIFF BOF记录 ($0809)
  while Stream.Position < MaxSearch - 4 do
  begin
    if Stream.Read(Buffer, 4) = 4 then
    begin
      if (Buffer[0] = $09) and (Buffer[1] = $08) then
      begin
        // 找到可能的BOF记录，回退到记录开始
        Stream.Position := Stream.Position - 4;
        ShowMessage('找到BIFF开始位置: ' + IntToStr(Stream.Position));
        Result := True;
        Exit;
      end;
      // 回退3字节，继续搜索
      Stream.Position := Stream.Position - 3;
    end
    else
      Break;
  end;

  // 如果没找到，尝试几个常见的OLE位置
  if not Result then
  begin
    ShowMessage('在前' + IntToStr(MaxSearch) + '字节中未找到BOF记录，尝试常见位置');

    // 尝试常见的OLE位置
    CommonPositions[0] := 512;
    CommonPositions[1] := 1024;
    CommonPositions[2] := 2048;
    CommonPositions[3] := 0;

    for I := 0 to High(CommonPositions) do
    begin
      if CommonPositions[I] < Stream.Size - 8 then
      begin
        Stream.Position := CommonPositions[I];
        if Stream.Read(Buffer, 4) = 4 then
        begin
          ShowMessage(Format('位置%d检查: %02X %02X %02X %02X',
            [CommonPositions[I], Buffer[0], Buffer[1], Buffer[2], Buffer[3]]));
          if (Buffer[0] = $09) and (Buffer[1] = $08) then
          begin
            Stream.Position := CommonPositions[I];
            ShowMessage('在位置' + IntToStr(CommonPositions[I]) + '找到BOF记录');
            Result := True;
            Exit;
          end;
        end;
      end;
    end;

    ShowMessage('所有位置都未找到BOF记录，文件可能不是标准的Excel格式');
  end;
end;

function TUnifiedExcelProcessor.ParseSSTRecord(Stream: TStream; RecordLength: Word; StringTable: TStringList): Boolean;
var
  Buffer: array[0..1023] of Byte; // 增大缓冲区
  TotalStrings: LongWord;
  UniqueStrings: LongWord;
  I: Integer;
  StringLen: Word;
  StringValue: string;
  AnsiStr: AnsiString;
  BytesRead: Integer;
  StartPos: Int64;
  FormatFlags: Byte;
  IsUnicode, HasFormatting, HasExtString: Boolean;
  ActualDataLength: Integer;
begin
  Result := False;
  StartPos := Stream.Position;

  try
    if RecordLength < 8 then
      Exit;

    // 读取SST头部
    Stream.Read(Buffer, 8);
    TotalStrings := Buffer[0] or (Buffer[1] shl 8) or (Buffer[2] shl 16) or (Buffer[3] shl 24);
    UniqueStrings := Buffer[4] or (Buffer[5] shl 8) or (Buffer[6] shl 16) or (Buffer[7] shl 24);

    // SST记录解析开始

    // 处理所有字符串，不限制数量
    for I := 0 to UniqueStrings - 1 do
    begin
      if Stream.Position >= StartPos + RecordLength then
        Break;

      // 读取字符串长度（2字节）
      if Stream.Read(Buffer, 2) = 2 then
      begin
        StringLen := Buffer[0] or (Buffer[1] shl 8);

        if (StringLen > 0) and (StringLen < 32000) then // 增加长度限制到32000
        begin
          // 读取格式标志位（1字节）
          if Stream.Read(FormatFlags, 1) = 1 then
          begin
            IsUnicode := (FormatFlags and $01) <> 0;
            HasFormatting := (FormatFlags and $04) <> 0;
            HasExtString := (FormatFlags and $08) <> 0;

            // 跳过格式信息（如果有）
            if HasFormatting then
              Stream.Seek(2, soCurrent); // 跳过格式运行计数
            if HasExtString then
              Stream.Seek(4, soCurrent); // 跳过扩展字符串长度

            // 读取实际字符串数据 - 使用动态缓冲区
            ActualDataLength := StringLen;
            if IsUnicode then
              ActualDataLength := StringLen * 2; // Unicode字符是2字节

            if (Stream.Position + ActualDataLength <= StartPos + RecordLength) then
            begin
              // 为长字符串分配动态缓冲区
              var StringBuffer: TBytes;
              SetLength(StringBuffer, ActualDataLength);
              BytesRead := Stream.Read(StringBuffer[0], ActualDataLength);
              if BytesRead = ActualDataLength then
              begin
                if IsUnicode then
                begin
                  // Unicode字符串处理
                  SetString(StringValue, PWideChar(@StringBuffer[0]), StringLen);
                end
                else
                begin
                  // ANSI字符串处理，使用正确的编码转换
                  SetString(AnsiStr, PAnsiChar(@StringBuffer[0]), StringLen);
                  StringValue := UTF8ToUnicodeString(AnsiStr);
                end;
                StringTable.Add(StringValue);
              end;
            end;
          end;
        end
        else
          Break;
      end
      else
        Break;
    end;

    Result := StringTable.Count > 0;

  except
    on E: Exception do
    begin
      // SST解析异常: E.Message
      Result := False;
    end;
  end;

  // 确保流位置正确
  Stream.Position := StartPos + RecordLength;
end;

function TUnifiedExcelProcessor.DecodeRKValue(B1, B2, B3, B4: Byte): string;
var
  RKValue: Cardinal;
  RawNumber: Int64;
  ResultValue: Double;
  TempInt64: Int64;
begin
  // 组合4字节的RK值（小端序）
  RKValue := B1 or (B2 shl 8) or (B3 shl 16) or (B4 shl 24);

  // RK值解码

  // 完全按照POI的RKUtil.decodeNumber实现
  // 去掉低2位标志位，获取实际数值部分
  RawNumber := RKValue shr 2;

  // 检查第1位（从0开始计数）：数据类型标志
  if (RKValue and $02) = $02 then
  begin
    // 整数类型：直接转换
    ResultValue := RawNumber;
  end
  else
  begin
    // IEEE 754浮点数：左移34位重建完整的64位浮点数
    // 这是POI算法的关键：raw_number << 34
    TempInt64 := RawNumber shl 34;
    // 将Int64的位模式解释为Double
    Move(TempInt64, ResultValue, SizeOf(Double));
  end;

  // 检查第0位：除以100标志
  if (RKValue and $01) = $01 then
    ResultValue := ResultValue / 100;

  Result := FloatToStr(ResultValue);
end;

function TUnifiedExcelProcessor.LoadFromFile(const FileName: string): Boolean;
var
  FileStream: TFileStream;
begin
  Result := False;
  if not FileExists(FileName) then
    Exit;

  try
    FileStream := TFileStream.Create(FileName, fmOpenRead or fmShareDenyNone);
    try
      FWorkbook.FileName := FileName;
      Result := LoadFromStream(FileStream);
    finally
      FileStream.Free;
    end;
  except
    on E: Exception do
    begin
      // 读取文件异常: E.Message
      Result := False;
    end;
  end;
end;

function TUnifiedExcelProcessor.LoadFromStream(Stream: TStream): Boolean;
var
  Buffer: array[0..7] of Byte;
  Sheet: TExcelSheet;
  I: Integer;
begin
  Result := False;
  if not Assigned(Stream) or (Stream.Size = 0) then
    Exit;

  try
    // 清理之前的数据
    FWorkbook.ClearSheets;

    // 读取文件头，检查是否为Excel文件
    Stream.Position := 0;
    if Stream.Read(Buffer, 8) = 8 then
    begin
      // 检查OLE文件头 (D0CF11E0A1B11AE1)
      if (Buffer[0] = $D0) and (Buffer[1] = $CF) and
         (Buffer[2] = $11) and (Buffer[3] = $E0) then
      begin
        // 检测到Excel格式，尝试解析BIFF数据
        if TryParseBiffData(Stream) then
          Result := True
        else
          Result := False;
      end;
    end;
  except
    on E: Exception do
    begin
      // 解析流异常: E.Message
      Result := False;
    end;
  end;
end;

function TUnifiedExcelProcessor.GetSheetCount: Integer;
begin
  Result := FWorkbook.GetSheetCount;
end;

function TUnifiedExcelProcessor.GetSheet(Index: Integer): TExcelSheet;
begin
  Result := FWorkbook.GetSheet(Index);
end;

function TUnifiedExcelProcessor.GetSheetNames: TStringList;
begin
  Result := FWorkbook.GetSheetNames;
end;

function TUnifiedExcelProcessor.SheetToDataSet(Index: Integer): TFDMemTable;
var
  Sheet: TExcelSheet;
begin
  Result := nil;
  Sheet := GetSheet(Index);
  if Assigned(Sheet) then
    Result := Sheet.ToDataSet;
end;

function TUnifiedExcelProcessor.IsValidExcelFile(const FileName: string): Boolean;
begin
  Result := FileExists(FileName) and
    (LowerCase(ExtractFileExt(FileName)) = '.xls');
end;

{ TExcelAPI }

class function TExcelAPI.ReadExcelFile(const FileName: string): TUnifiedExcelProcessor;
begin
  Result := TUnifiedExcelProcessor.Create;
  if not Result.LoadFromFile(FileName) then
  begin
    Result.Free;
    Result := nil;
  end;
end;

class function TExcelAPI.ReadSheetToDataSet(const FileName: string; SheetIndex: Integer): TFDMemTable;
var
  Processor: TUnifiedExcelProcessor;
begin
  Result := nil;
  Processor := ReadExcelFile(FileName);
  if Assigned(Processor) then
  try
    Result := Processor.SheetToDataSet(SheetIndex);
  finally
    Processor.Free;
  end;
end;

class function TExcelAPI.ReadSheetToDataSet(const FileName: string; const SheetName: string): TFDMemTable;
var
  Processor: TUnifiedExcelProcessor;
begin
  Result := nil;
  Processor := ReadExcelFile(FileName);
  if Assigned(Processor) then
  try
    Result := Processor.SheetToDataSet(0); // 简化实现，总是返回第一个工作表
  finally
    Processor.Free;
  end;
end;

class function TExcelAPI.GetSheetNames(const FileName: string): TStringList;
var
  Processor: TUnifiedExcelProcessor;
  Names: TStringList;
begin
  Result := TStringList.Create;
  Processor := ReadExcelFile(FileName);
  if Assigned(Processor) then
  try
    Names := Processor.GetSheetNames;
    try
      Result.Assign(Names);
    finally
      Names.Free;
    end;
  finally
    Processor.Free;
  end;
end;

class function TExcelAPI.IsValidExcelFile(const FileName: string): Boolean;
var
  Processor: TUnifiedExcelProcessor;
begin
  Result := False;
  if FileExists(FileName) then
  begin
    Processor := TUnifiedExcelProcessor.Create;
    try
      Result := Processor.IsValidExcelFile(FileName);
    finally
      Processor.Free;
    end;
  end;
end;

class function TExcelAPI.GetExcelFileInfo(const FileName: string): string;
var
  Processor: TUnifiedExcelProcessor;
  Names: TStringList;
begin
  Result := '';
  Processor := TUnifiedExcelProcessor.Create;
  try
    if Processor.LoadFromFile(FileName) then
    begin
      Names := Processor.GetSheetNames;
      try
        Result := Format('文件: %s'#13#10'工作表数量: %d'#13#10'工作表列表: %s',
          [string(ExtractFileName(FileName)), Processor.GetSheetCount, string(Names.CommaText)]);
      finally
        Names.Free;
      end;
    end
    else
      Result := '无法读取文件: ' + ExtractFileName(FileName);
  finally
    Processor.Free;
  end;
end;

end.
