# NewDBTool 配置文件
# 可以直接编辑此文件来修改设置

[Display] # 显示设置
FontSize=9  # 字体大小 (8-24)
FontName=Tahoma  # 字体名称 (<PERSON><PERSON><PERSON>, <PERSON><PERSON>, Microsoft YaHei, SimSun)
ShowGrid=True  # 显示网格线 (True/False)
ShowHeaders=True  # 显示列标题 (True/False)

[Colors] # 颜色设置
SelectionColor=13166335  # 选择背景色 (RGB整数值)
SelectionTextColor=0  # 选择文字色 (RGB整数值)
HeaderColor=16775920  # 标题背景色 (RGB整数值)
ButtonColor=15790320  # 按钮背景色 (RGB整数值)

[Behavior] # 行为设置
AutoSave=True  # 自动保存 (True/False)
ConfirmDelete=True  # 删除确认 (True/False)
EnableFormulas=True  # 启用公式 (True/False)
DoubleClickEdit=True  # 双击编辑 (True/False)

[ColorButton] # 颜色按钮设置
ButtonWidth=30  # 按钮宽度像素 (10-100)
ButtonText=...  # 按钮显示文字
ShowButton=True  # 显示颜色按钮 (True/False)

# ========================================
# 常用颜色值参考：
# ========================================
# 白色: 16777215
# 黑色: 0
# 红色: 255
# 绿色: 65280
# 蓝色: 16711680
# 浅蓝色: 13166335
# 深蓝色: 4210752
# 浅灰色: 15790320
# 深灰色: 8421504
# 黄色: 65535
# 紫色: 16711935
# 青色: 16776960

# ========================================
# 预设主题配置：
# ========================================

# 深色主题 - 适合长时间使用
# FontSize=10
# FontName=Microsoft YaHei
# SelectionColor=4210752
# SelectionTextColor=16777215
# HeaderColor=3158064
# ButtonColor=5263440

# 简洁模式 - 最小化界面
# FontSize=8
# FontName=Tahoma
# ShowGrid=False
# SelectionColor=16777215
# SelectionTextColor=0
# HeaderColor=15790320
# ButtonColor=15790320

# 传奇风格 - 经典游戏风格
# FontSize=9
# FontName=SimSun
# SelectionColor=255
# SelectionTextColor=16777215
# HeaderColor=8388608
# ButtonColor=12632256

# ========================================
# 使用说明：
# ========================================
# 1. 复制想要的配置到对应的节中
# 2. 保存文件
# 3. 在程序中点击"是"重新加载配置
# 4. 配置立即生效！

# 注意：
# - True/False 区分大小写
# - 颜色值必须是有效的RGB整数
# - 字体名称必须是系统已安装的字体
# - 数值必须在有效范围内
