program XLSTestDebug;

{$APPTYPE CONSOLE}

uses
  System.SysUtils,
  System.Classes,
  System.Math,
  XLSReader,
  BxlsReader;

function GetFileSize(const FileName: string): Int64;
procedure TestXLSFile(const FileName: string);
var
  XLSReader: TXLSReader;
  SheetNames: TStringList;
  i, j: Integer;
  BxlsReader: BTXlsReader;
  SheetCount: LongWord;
  SheetName: string;
  CellValue: WideString;
  MaxCol, MaxRow: LongWord;
begin
  WriteLn('=== 测试XLS文件读取 ===');
  WriteLn('文件: ' + FileName);
  WriteLn('');

  if not FileExists(FileName) then
  begin
    WriteLn('错误: 文件不存在');
    Exit;
  end;

  WriteLn('文件大小: ' + IntToStr(GetFileSize(FileName)) + ' 字节');
  WriteLn('');

  // 测试1: 使用BxlsReader直接读取
  WriteLn('=== 测试1: BxlsReader直接读取 ===');
  BxlsReader := BTXlsReader.Create;
  try
    if BxlsReader.OpenFile(FileName, nil, 0) then
    begin
      WriteLn('✓ BxlsReader成功打开文件');
      
      SheetCount := BxlsReader.GetSheetsCount;
      WriteLn('工作表数量: ' + IntToStr(SheetCount));
      
      for i := 1 to SheetCount do
      begin
        SheetName := BxlsReader.GetSheetName(i);
        WriteLn('工作表 ' + IntToStr(i) + ': ' + SheetName);
        
        if BxlsReader.SelectSheet(i) then
        begin
          if BxlsReader.GetSheetBounds(MaxCol, MaxRow) then
          begin
            WriteLn('  范围: ' + IntToStr(MaxCol) + ' 列 x ' + IntToStr(MaxRow) + ' 行');
            
            // 读取前几个单元格
            for j := 1 to Min(5, MaxRow) do
            begin
              if BxlsReader.GetCellValue(1, j, CellValue) then
                WriteLn('  单元格[1,' + IntToStr(j) + ']: ' + CellValue)
              else
                WriteLn('  单元格[1,' + IntToStr(j) + ']: <空>');
            end;
          end
          else
            WriteLn('  无法获取工作表范围');
        end
        else
          WriteLn('  无法选择工作表');
      end;
    end
    else
    begin
      WriteLn('✗ BxlsReader无法打开文件');
    end;
  finally
    BxlsReader.Free;
  end;

  WriteLn('');

  // 测试2: 使用XLSReader
  WriteLn('=== 测试2: XLSReader读取 ===');
  XLSReader := TXLSReader.Create;
  try
    if XLSReader.LoadFromFile(FileName) then
    begin
      WriteLn('✓ XLSReader成功加载文件');
      
      SheetNames := XLSReader.GetSheetNames;
      try
        WriteLn('工作表数量: ' + IntToStr(SheetNames.Count));
        
        for i := 0 to SheetNames.Count - 1 do
        begin
          WriteLn('工作表 ' + IntToStr(i + 1) + ': ' + SheetNames[i]);
        end;
        
        if SheetNames.Count > 0 then
        begin
          WriteLn('');
          WriteLn('读取第一个工作表数据...');
          // 简化测试，只显示工作表信息
          WriteLn('✓ 工作表信息读取成功');
        end;
      finally
        SheetNames.Free;
      end;
    end
    else
    begin
      WriteLn('✗ XLSReader无法加载文件');
      WriteLn('错误: ' + XLSReader.LastError);
    end;
  finally
    XLSReader.Free;
  end;
end;

function GetFileSize(const FileName: string): Int64;
var
  FileStream: TFileStream;
begin
  try
    FileStream := TFileStream.Create(FileName, fmOpenRead or fmShareDenyWrite);
    try
      Result := FileStream.Size;
    finally
      FileStream.Free;
    end;
  except
    Result := -1;
  end;
end;

var
  FileName: string;
begin
  try
    WriteLn('XLS文件读取调试工具');
    WriteLn('==================');
    WriteLn('');

    if ParamCount > 0 then
      FileName := ParamStr(1)
    else
      FileName := 'Money.xls';

    TestXLSFile(FileName);

    WriteLn('');
    WriteLn('测试完成，按回车键退出...');
    ReadLn;
  except
    on E: Exception do
    begin
      WriteLn('程序异常: ' + E.ClassName + ': ' + E.Message);
      WriteLn('按回车键退出...');
      ReadLn;
    end;
  end;
end.
